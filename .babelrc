{"env": {"production": {"plugins": ["transform-remove-console"]}}, "presets": ["module:@react-native/babel-preset"], "plugins": [["@babel/plugin-proposal-decorators", {"legacy": true}], ["module-resolver", {"app": ["./src/App.tsx"], "alias": {"@app": "./src/App.tsx", "@constants": "./src/constants/index", "@styles": "./src/styles/index", "@components": "./src/components/index", "@common": "./src/common/index", "@elements": "./src/elements/index", "@utils": "./src/utils/index", "@types": "./src/types/index", "@hooks": "./src/hooks/index"}}, "react-native-reanimated/plugin"]]}