# OSX
#
.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.zip
*.xcuserstate
project.xcworkspace
# root.js

# react-native-config codegen
ios/tmp.xcconfig

# Android/IntelliJ
#
build/
.idea
.gradle
local.properties
*.iml

# node.js
#
node_modules/
npm-debug.log
yarn-error.log
.yarn/*

# BUCK
buck-out/
\.buckd/
*.keystore
android/Gemfile.lock
android/*.hprof
!debug.keystore

# fastlane
#
# It is recommended to not store the screenshots in the git repo. Instead, use fastlane to re-generate the
# screenshots whenever they are needed.
# For more information about the recommended setup visit:
# https://docs.fastlane.tools/best-practices/source-control/

*/vendor

/android/Gemfile.lock

# Bundle artifact
*.jsbundle

# CocoaPods
/ios/assets
/ios/Pods/
/package-lock.json
/__tests__
.eslintcache
# generated by bob
lib/
