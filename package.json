{"name": "payment_approval", "version": "2.0.19", "version-live": "2.0.20", "private": true, "appName": "XWORK", "moduleName": "Finace", "featureName": "PaymentApproval", "workspaces": {"packages": ["packages/*"]}, "scripts": {"start": "STANDALONE=1 react-native webpack-start --host 127.0.0.1 --port 9000", "build": "bash buildBundle.sh", "start-prod": "react-native webpack-start --port 9000 --webpackConfig webpack.prod.config.mjs"}, "dependencies": {"@bam.tech/react-native-image-resizer": "^3.0.11", "@callstack-mwg/repack": "4.4.1", "@mwg-kits/common": "^0.0.5", "@mwg-kits/components": "0.1.25--beta", "@mwg-kits/core": "^0.0.1", "@mwg-sdk/styles": "^1.0.5", "@react-native-async-storage/async-storage": "^1.17.10", "@react-native-camera-roll/camera-roll": "^7.10.0", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.9.9", "@react-navigation/stack": "^6.3.29", "apollo3-cache-persist": "0.15.0", "eslint-plugin-import": "^2.27.5", "moment": "^2.30.1", "react": "18.2.0", "react-native": "0.73.6", "react-native-background-timer": "^2.4.1", "react-native-blob-util": "^0.19.8", "react-native-config": "1.5.0", "react-native-device-info": "^10.3.0", "react-native-document-picker": "^9.0.1", "react-native-file-viewer": "^2.1.5", "react-native-fs": "^2.16.6", "react-native-gesture-handler": "^2.17.1", "react-native-image-crop-picker": "^0.42.0", "react-native-image-zoom-viewer": "^3.0.1", "react-native-keyboard-aware-scroll-view": "^0.9.1", "react-native-mmkv": "2.12.2", "react-native-modal": "13.0.1", "react-native-modal-datetime-picker": "17.0.0", "react-native-permissions": "^3.8.0", "react-native-safe-area-context": "4.10.1", "react-native-safe-area-view": "^1.1.1", "react-native-sensitive-info": "5.5.8", "react-native-vector-icons": "10.1.0", "react-native-xwork": "https://sourceapp.tgdd.vn/SuperXwork/react-native-xwork.git#develop", "react-redux": "^8.0.4", "redux": "^4.2.0", "redux-logger": "^3.0.6", "redux-thunk": "^2.4.1", "rn-background-timer-id": "2.4.3", "ts-loader": "^9.4.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-decorators": "^7.23.9", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@mwg/eslint-config-react": "https://sourceapp.tgdd.vn/appplugins/eslint-config.git", "@mwg/prettier-config": "https://sourceapp.tgdd.vn/appplugins/prettier-config.git", "@tsconfig/react-native": "^3.0.5", "@types/jest": "^29.5.12", "@types/node": "^22.7.0", "@types/react": "^18.3.3", "@types/react-native": "^0.73.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.3.0", "babel-jest": "^26.6.3", "babel-loader": "^9.1.2", "babel-plugin-module-resolver": "^4.0.0", "babel-plugin-transform-remove-console": "^6.9.4", "compression-webpack-plugin": "^10.0.0", "eslint-plugin-jsx-a11y": "^6.7.1", "husky": ">=6", "jest": "^26.6.3", "lint-staged": ">=10", "metro-react-native-babel-preset": "0.72.3", "react-native-builder-bob": "^0.30.2", "react-test-renderer": "18.1.0", "terser-webpack-plugin": "^5.3.6", "typescript": "^5.5.4", "webpack": "^5.91.0"}, "source": "./src/App.tsx", "module": "./lib/module/index.js", "types": "./lib/typescript/commonjs/src/index.d.ts", "files": ["src", "lib", "!**/__tests__", "!**/__fixtures__", "!**/__mocks__"], "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["commonjs", {"esm": true}], ["module", {"esm": true}], ["typescript", {"esm": true}]]}, "eslintIgnore": ["node_modules/", "lib/"]}