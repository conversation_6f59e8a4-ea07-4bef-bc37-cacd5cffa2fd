import React from 'react';
import { Provider } from 'react-redux';
import { store } from '@store';
import {
    SafeAreaProvider,
    SafeAreaInsetsContext
} from 'react-native-safe-area-context';
import { NavigationContainer } from '@react-navigation/native';
import MainNavigator from './navigation';
import { CustomToast } from '@components';
import Toast from 'react-native-toast-message';
const App = (props: any) => {
    return (
        <SafeAreaProvider>
            <Provider store={store}>
                <NavigationContainer>
                    <SafeAreaInsetsContext.Consumer>
                        {(insets: any) => (
                            <MainNavigator {...insets} {...props} />
                        )}
                    </SafeAreaInsetsContext.Consumer>
                    <Toast config={CustomToast} />
                </NavigationContainer>
            </Provider>
        </SafeAreaProvider>
    );
};

export default App;
