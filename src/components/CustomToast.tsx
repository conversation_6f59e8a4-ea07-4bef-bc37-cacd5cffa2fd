import React from 'react';
import { View, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { BaseToast } from 'react-native-toast-message';
import { Mixins, XworkColor } from '@mwg-sdk/styles';
import { IMAGES } from '@assets';

export const CustomToast = {
    success: (props: any) => {
        return props.isVisible ? (
            <View style={[style.toastSuccess, { backgroundColor: '#E3F9E5' }]}>
                <Image
                    source={{ uri: 'ic_success' }}
                    style={Mixins.scaleImage(20, 20)}
                    resizeMode="contain"
                />
                <BaseToast
                    {...props}
                    style={[
                        style.baseToastSuccess,
                        { backgroundColor: '#E3F9E5' }
                    ]}
                    contentContainerStyle={{
                        paddingLeft: Mixins.scale(6)
                    }}
                    text1Style={{
                        fontSize: 15,
                        fontWeight: '500',
                        color: '#27BE50'
                    }}
                    text1NumberOfLines={2}
                    text2Style={{
                        fontSize: 14,
                        fontWeight: '500',
                        color: '#27BE50'
                    }}
                    text2NumberOfLines={2}
                />
                <TouchableOpacity
                    onPress={() => {
                        props.hide();
                    }}>
                    <Image
                        source={{ uri: 'ic_close_cmt' }}
                        style={[
                            Mixins.scaleImage(20, 20),
                            { tintColor: '#27BE50' }
                        ]}
                        resizeMode="contain"
                    />
                </TouchableOpacity>
            </View>
        ) : null;
    },
    error: (props: any) => {
        return props.isVisible ? (
            <View
                style={[
                    style.toastSuccess,
                    {
                        backgroundColor: '#FFE3E3',
                        height: 'auto',
                        paddingVertical: Mixins.scale(4)
                    }
                ]}>
                <Image
                    source={IMAGES.ic_close}
                    style={Mixins.scaleImage(20, 20)}
                    resizeMode="contain"
                />
                <BaseToast
                    {...props}
                    style={[
                        style.baseToastSuccess,
                        {
                            backgroundColor: '#FFE3E3',
                            height: 'auto',
                            paddingVertical: Mixins.scale(4)
                        }
                    ]}
                    contentContainerStyle={{
                        paddingLeft: Mixins.scale(6)
                    }}
                    text1Style={{
                        fontSize: 15,
                        fontWeight: '500',
                        color: '#E12D39'
                    }}
                    text2Style={{
                        fontSize: 14,
                        fontWeight: '500',
                        color: '#E12D39'
                    }}
                    text2NumberOfLines={99}
                />
                <TouchableOpacity
                    onPress={() => {
                        props.hide();
                    }}>
                    <Image
                        source={{ uri: 'ic_close_cmt' }}
                        style={[
                            Mixins.scaleImage(20, 20),
                            { tintColor: '#E12D39' }
                        ]}
                        resizeMode="contain"
                    />
                </TouchableOpacity>
            </View>
        ) : null;
    },

    warning: (props: any) => {
        return props.isVisible ? (
            <View style={[style.toastSuccess, { backgroundColor: '#FFFBEA' }]}>
                <Image
                    source={IMAGES.ic_warning_circle}
                    style={Mixins.scaleImage(20, 20)}
                    resizeMode="contain"
                />
                <BaseToast
                    {...props}
                    text1NumberOfLines={3}
                    style={[
                        style.baseToastSuccess,
                        { backgroundColor: '#FFFBEA' }
                    ]}
                    contentContainerStyle={{
                        paddingLeft: Mixins.scale(6)
                    }}
                    text1Style={{
                        fontSize: 15,
                        fontWeight: '500',
                        color: '#DE911D'
                    }}
                />
                <TouchableOpacity
                    onPress={() => {
                        props.hide();
                    }}>
                    <Image
                        source={{ uri: 'ic_close_cmt' }}
                        style={[
                            Mixins.scaleImage(20, 20),
                            { tintColor: '#DE911D' }
                        ]}
                        resizeMode="contain"
                    />
                </TouchableOpacity>
            </View>
        ) : null;
    },
    loading: (props: any) => {
        return props.isVisible ? (
            <View style={[style.toastSuccess, { backgroundColor: '#E6F6FF' }]}>
                <Image
                    source={IMAGES.ic_warning_circle}
                    style={[
                        Mixins.scaleImage(20, 20),
                        { tintColor: '#2186EB' }
                    ]}
                    resizeMode="contain"
                />
                <BaseToast
                    {...props}
                    text1NumberOfLines={3}
                    style={[
                        style.baseToastSuccess,
                        { backgroundColor: '#E6F6FF' }
                    ]}
                    contentContainerStyle={{
                        paddingLeft: Mixins.scale(6)
                    }}
                    text1Style={{
                        fontSize: 15,
                        fontWeight: '500',
                        color: '#2186EB'
                    }}
                />
                <TouchableOpacity
                    onPress={() => {
                        props.hide();
                    }}>
                    <Image
                        source={{ uri: 'ic_close_cmt' }}
                        style={[
                            Mixins.scaleImage(20, 20),
                            { tintColor: '#2186EB' }
                        ]}
                        resizeMode="contain"
                    />
                </TouchableOpacity>
            </View>
        ) : null;
    }
};

const bg_color = '#9D9D9D';

const style = StyleSheet.create({
    baseToastSuccess: {
        backgroundColor: bg_color,
        borderLeftWidth: 0,
        elevation: 3,
        flex: 1,
        height: Mixins.scale(42),
        shadowColor: XworkColor.TRANSAPARENT,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0,
        shadowRadius: 0
    },
    toastSuccess: {
        alignItems: 'center',
        backgroundColor: bg_color,
        borderRadius: Mixins.scale(12),
        flexDirection: 'row',
        marginHorizontal: Mixins.scale(16),
        paddingHorizontal: Mixins.scale(12),
        paddingVertical: Mixins.scale(4)
    }
});
