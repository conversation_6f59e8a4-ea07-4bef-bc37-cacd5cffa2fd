import DocumentPicker, {
    type DocumentPickerResponse
} from 'react-native-document-picker';
import Permissions from 'react-native-permissions';
import { Platform } from 'react-native';
import { Colors } from 'react-native-xwork';
import { openSettings } from 'react-native-permissions';
import FileViewer from 'react-native-file-viewer';
import RNFS from 'react-native-fs';
const { translate } = (global as any).props.getTranslateConfig();

export const GENERAL_SUPPORTED_DOCS_FORMAT = [
    'data:@file/pdf',
    'data:@file/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
];

export const hasStoragePermission = async () => {
    if (Platform.OS === 'android' && Platform.Version < 32) {
        const storagePermission =
            Permissions.PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE;
        let permissionRequest;
        const hasPermissionToStorage = await Permissions.check(
            storagePermission
        );
        switch (hasPermissionToStorage) {
            case Permissions.RESULTS.DENIED:
                permissionRequest = await Permissions.request(
                    storagePermission
                );
                return permissionRequest === Permissions.RESULTS.GRANTED;
            case Permissions.RESULTS.BLOCKED: {
                return permissionRequest === Permissions.RESULTS.BLOCKED;
            }
            default:
                return true;
        }
    }

    return true;
};
export const filePicker = async () => {
    return new Promise<DocumentPickerResponse[]>(async (resolve, reject) => {
        try {
            // Check for storage permission
            const permissionGranted = await hasStoragePermission();
            if (!permissionGranted) {
                (global as any).props.alert({
                    show: true,
                    title: translate(`notify`),
                    message: translate(`storage_permission`),
                    confirmText: translate(`close`),
                    cancelText: translate(`setting`),
                    confirmButtonTextStyle: {
                        color: Colors.BRAND_600
                    },
                    cancelButtonTextStyle: { color: Colors.BRAND_600 },
                    onConfirmPressed: () => {
                        (global as any).props.alert({ show: false });
                    },
                    onCancelPressed: () => {
                        (global as any).props.alert({ show: false });
                        openSettings();
                    }
                });
                return;
            }

            const res: DocumentPickerResponse[] = await DocumentPicker.pick({
                type: [DocumentPicker.types.pdf, DocumentPicker.types.xlsx],
                presentationStyle: 'fullScreen'
            });

            if (res?.length === 0) {
                reject(new Error('No document selected'));
                return;
            }

            resolve(res);
        } catch (error) {
            if (DocumentPicker.isCancel(error)) {
                console.log('User canceled document picker');
                reject(new Error('User canceled document picker'));
            } else {
                console.error('File upload error:', error);
                reject(error);
            }
        }
    });
};

export const openDocument = async (fileUri: string, fileType: string) => {
    try {
        let filePath = fileUri;

        if (Platform.OS === 'android' && fileUri.startsWith('content://')) {
            const destPath = `${RNFS.CachesDirectoryPath}/temp.${
                fileType === 'application/pdf' ? 'pdf' : 'xlsx'
            }`;
            await RNFS.copyFile(fileUri, destPath);
            filePath = destPath;
        }

        await FileViewer.open(filePath, { showOpenWithDialog: true });
    } catch (error: any) {
        console.log(`Cannot open file: ${error.message}`);
        const msg = error.message.includes(
            'No app associated with this mime type'
        )
            ? 'Không tìm thấy ứng dụng phù hợp để mở file này.'
            : error.message;
        (global as any).props.alert({
            show: true,
            title: translate(`error`),
            message: msg,
            confirmText: translate(`close`),
            confirmButtonTextStyle: {
                color: Colors.BRAND_600
            },
            onConfirmPressed: () => {
                (global as any).props.alert({ show: false });
            }
        });
    }
};
