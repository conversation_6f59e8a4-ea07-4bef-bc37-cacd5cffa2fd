import { Platform } from 'react-native';
import ImagePicker from 'react-native-image-crop-picker';
import ImageResizer from '@bam.tech/react-native-image-resizer';
import RNFetchBlob from 'react-native-blob-util';
import { Colors } from 'react-native-xwork';
import { requestPermission, openSetting } from '@mwg-kits/core';
const { translate } = (global as any).props.getTranslateConfig();

export const isImage = (media: any) =>
    !(media.mime && media.mime.toLowerCase().indexOf('video/') !== -1);

const _showCamera = () => {
    return new Promise(async (resolve, reject) => {
        try {
            const optionPicker = {
                mediaType: 'photo',
                compressImageQuality: 1,
                includeBase64: true,
                compressImageMaxHeight: 1000,
                compressImageMaxWidth: 1000
            };
            const image = await ImagePicker.openCamera({
                ...optionPicker
            });

            if (image) {
                if (isImage(image)) {
                    if (Platform.OS === 'android') {
                        ImageResizer.createResizedImage(
                            image.path,
                            image.width,
                            image.height,
                            'JPEG',
                            90,
                            0,
                            RNFetchBlob.fs.dirs.DocumentDir
                        )
                            .then(async (response) => {
                                const base64 = await RNFetchBlob.fs.readFile(
                                    response.path,
                                    'base64'
                                );
                                resolve([
                                    {
                                        fileName: response.name.toLowerCase(),
                                        base64: base64
                                    }
                                ]);
                            })
                            .catch((error) => {
                                console.log('ERROR==', error);
                            });
                    } else {
                        resolve([
                            {
                                fileName: image.path
                                    .replace(/^.*[\\/]/, '')
                                    .toLowerCase(),
                                base64: image.data
                            }
                        ]);
                    }
                }
            }
        } catch (e) {
            reject(e);
        }
    });
};

// const _showLibraryPicker = () => {
//     return new Promise((resolve, reject) => {
//         ImagePicker.openPicker({
//             multiple: true,
//             includeBase64: true,
//             mediaType: 'photo',
//             compressImageMaxWidth: 1000,
//             compressImageMaxHeight: 1000,
//             compressImageQuality: 1,
//             maxFiles: 20
//         })
//             .then(async (images) => {
//                 let arrayImg = [];
//                 for (let i = 0; i < images.length; i++) {
//                     const path =
//                         Platform.OS === 'android'
//                             ? images[i].path
//                             : images[i].sourceURL;
//                     ImageResizer.createResizedImage(
//                         path,
//                         images[i].width,
//                         images[i].height,
//                         'JPEG',
//                         90,
//                         0,
//                         RNFetchBlob.fs.dirs.DocumentDir
//                     )
//                         .then(async (response) => {
//                             const base64 = await RNFetchBlob.fs.readFile(
//                                 response.path,
//                                 'base64'
//                             );
//                             let obj = {
//                                 fileName: response.name.toLowerCase(),
//                                 base64: base64
//                             };
//                             arrayImg.push(obj);
//                             if (arrayImg.length === images.length) {
//                                 resolve(arrayImg);
//                             }
//                         })
//                         .catch((error) => {
//                             console.log('ERROR==', error);
//                         });
//                 }
//             })
//             .catch((err) => {
//                 reject(err);
//                 console.log('errShowLibrary', err);
//             });
//     });
// };

const _showLibraryPicker = (isMultiple = true) => {
    return new Promise((resolve, reject) => {
        ImagePicker.openPicker({
            multiple: isMultiple,
            includeBase64: true,
            mediaType: 'photo',
            compressImageMaxWidth: 1000,
            compressImageMaxHeight: 1000,
            compressImageQuality: 1,
            maxFiles: isMultiple ? 20 : 1
        })
            .then(async (images) => {
                let arrayImg = [];
                images = Array.isArray(images) ? images : [images];

                for (let i = 0; i < images.length; i++) {
                    const path =
                        Platform.OS === 'android'
                            ? images[i].path
                            : images[i].sourceURL;
                    ImageResizer.createResizedImage(
                        path,
                        images[i].width,
                        images[i].height,
                        'JPEG',
                        90,
                        0,
                        RNFetchBlob.fs.dirs.DocumentDir
                    )
                        .then(async (response) => {
                            const base64 = await RNFetchBlob.fs.readFile(
                                response.path,
                                'base64'
                            );
                            let obj = {
                                fileName: response.name.toLowerCase(),
                                base64: base64
                            };
                            arrayImg.push(obj);
                            if (arrayImg.length === images?.length) {
                                resolve(isMultiple ? arrayImg : arrayImg[0]);
                            }
                        })
                        .catch((error) => {
                            console.log('ERROR==', error);
                        });
                }
            })
            .catch((err) => {
                reject(err);
                console.log('errShowLibrary', err);
            });
    });
};

export const PermissionCamera = async (callback: any) => {
    try {
        await requestPermission('camera');
        _showCamera()
            .then((response) => {
                console.log('RESPONSE==', response);
                callback(response, '');
            })
            .catch((error) => {
                callback([], error);
            });
    } catch (error) {
        (global as any).props.alert({
            show: true,
            title: translate(`notify`),
            message: translate(`camera_permission`),
            confirmText: translate(`close`),
            cancelText: translate(`setting`),
            confirmButtonTextStyle: {
                color: Colors.BRAND_600
            },
            cancelButtonTextStyle: { color: Colors.BRAND_600 },
            onConfirmPressed: () => {
                (global as any).props.alert({ show: false });
            },
            onCancelPressed: () => {
                (global as any).props.alert({ show: false });
                openSetting();
            }
        });
    }
};

export const PermissionPhoto = async (callback: any) => {
    try {
        await requestPermission('photo');
        _showLibraryPicker(false)
            .then((response) => {
                console.log('RESPONSE==', response);
                callback(response, '');
            })
            .catch((error) => {
                callback([], error);
            });
    } catch (error) {
        (global as any).props.alert({
            show: true,
            title: translate(`notify`),
            message: translate(`photo_permission`),
            confirmText: translate(`close`),
            cancelText: translate(`setting`),
            confirmButtonTextStyle: {
                color: Colors.BRAND_600
            },
            cancelButtonTextStyle: { color: Colors.BRAND_600 },
            onConfirmPressed: () => {
                (global as any).props.alert({ show: false });
            },
            onCancelPressed: () => {
                (global as any).props.alert({ show: false });
                openSetting();
            }
        });
    }
};
