import React from 'react';
import {
    TouchableOpacity,
    View,
    StyleSheet,
    Platform,
    StatusBar,
    Image,
    ActivityIndicator
} from 'react-native';
import { Mixins } from '@mwg-sdk/styles';
import { Colors } from 'react-native-xwork';
import RNFetchBlob from 'react-native-blob-util';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import { requestPermission, openSetting } from '@mwg-kits/core';
import <PERSON>Viewer from 'react-native-image-zoom-viewer';
import { IImageInfo } from 'react-native-image-zoom-viewer/built/image-viewer.type';
import { CustomToast } from './CustomToast';
import Toast from 'react-native-toast-message';

const { translate } = (global as any).props.getTranslateConfig();

export const ImageView = ({
    imgList,
    onPressClose,
    indexImage
}: {
    imgList: IImageInfo[];
    onPressClose: () => void;
    indexImage: number;
}) => {
    const downloadImage = async (image: string) => {
        try {
            const imageName = image.substring(image.lastIndexOf('/') + 1);
            const { fs } = RNFetchBlob;
            const dirs = fs.dirs;
            const filePath = `${dirs.DocumentDir}/${imageName}`;
            console.log('get image name: ', imageName);
            if (Platform.OS == 'android') {
                RNFetchBlob.config({
                    fileCache: true,
                    appendExt: 'jpg',
                    indicator: true,
                    path: filePath,
                    addAndroidDownloads: {
                        useDownloadManager: true,
                        notification: true,
                        path: `${dirs.PictureDir}/${imageName}`,
                        description: 'Image',
                        mime: 'image/jpeg'
                    }
                })
                    .fetch('GET', image)
                    .then(
                        () => {
                            Toast.show({
                                type: 'success',
                                text1: translate(`photo_saved`),
                                position: 'bottom'
                            });
                        },
                        (error) => {
                            console.log('ERROR==', error);
                            Toast.show({
                                type: 'error',
                                text1: translate(`failed_to_save_photo`),
                                position: 'bottom'
                            });
                        }
                    );
            } else {
                const res = await RNFetchBlob.config({
                    fileCache: true,
                    path: filePath
                }).fetch('GET', image);

                const localFilePath = res.path();
                console.log('get file path', localFilePath);
                await saveImageToCameraRoll(`file://${localFilePath}`);
            }
        } catch (error) {
            console.error('Download Error:', error);
            Toast.show({
                type: 'error',
                text1: translate('failed_to_save_photo'),
                position: 'bottom'
            });
        }
    };

    const saveImageToCameraRoll = async (image: string) => {
        try {
            await CameraRoll.save(image, { type: 'photo' });
            Toast.show({
                type: 'success',
                text1: translate('photo_saved'),
                position: 'bottom'
            });
        } catch (error) {
            console.log('ERROR==', error);
            Toast.show({
                type: 'error',
                text1: translate('failed_to_save_photo'),
                position: 'bottom'
            });
        }
    };

    const checkPermission = async (image: string) => {
        try {
            await requestPermission('storage');
            downloadImage(image);
        } catch (err) {
            (global as any).props.alert({
                show: true,
                title: translate(`notify`),
                message: translate(`storage_permission`),
                confirmText: translate(`close`),
                cancelText: translate(`setting`),
                confirmButtonTextStyle: {
                    color: Colors.BRAND_600
                },
                cancelButtonTextStyle: { color: Colors.BRAND_600 },
                onConfirmPressed: () => {
                    (global as any).props.alert({ show: false });
                },
                onCancelPressed: () => {
                    (global as any).props.alert({ show: false });
                    openSetting();
                }
            });
        }
    };

    return (
        <View
            style={[
                Styles.container,
                {
                    paddingBottom: Mixins.scale(32),
                    paddingTop: Platform.OS === 'ios' ? Mixins.scale(34) : 0
                }
            ]}>
            <StatusBar backgroundColor={Colors.BLACK} />
            <ImageViewer
                imageUrls={imgList}
                index={indexImage}
                enableSwipeDown={true}
                onSwipeDown={onPressClose}
                saveToLocalByLongPress={false}
                loadingRender={() => {
                    return (
                        <View style={Styles.empty}>
                            <ActivityIndicator
                                color={Colors.WHITE}
                                size="small"
                                style={{ paddingVertical: 20 }}
                            />
                        </View>
                    );
                }}
                renderImage={(item) => {
                    return (
                        <View style={{ flex: 1 }}>
                            <Image
                                source={{
                                    uri: item.source.uri
                                }}
                                style={{
                                    width: item.style.width,
                                    height: item.style.height
                                }}
                                resizeMode="contain"
                            />
                        </View>
                    );
                }}
            />

            <View
                style={[
                    Styles.header,
                    {
                        top:
                            Platform.OS === 'ios'
                                ? Mixins.scale(70)
                                : Mixins.scale(34)
                    }
                ]}>
                <TouchableOpacity
                    onPress={onPressClose}
                    style={Styles.btnClose}>
                    <Image
                        source={{ uri: 'ic_close' }}
                        style={Mixins.scaleImage(9, 9)}
                        resizeMode="contain"
                    />
                </TouchableOpacity>

                <TouchableOpacity
                    onPress={() => {
                        // checkPermission(imageUrl);
                        checkPermission(imgList[0].url);
                    }}>
                    <Image
                        source={{ uri: 'ic_download' }}
                        style={Mixins.scaleImage(28, 28)}
                        resizeMode="contain"
                    />
                </TouchableOpacity>
            </View>
            <Toast config={CustomToast} />
        </View>
    );
};

const Styles = StyleSheet.create({
    btnClose: {
        alignItems: 'center',
        backgroundColor: Colors.WHITE,
        borderRadius: 28 / 2,
        height: Mixins.scale(28),
        justifyContent: 'center',
        width: Mixins.scale(28)
    },
    container: {
        backgroundColor: Colors.BLACK,
        flex: 1
    },

    empty: {
        alignItems: 'center',
        flex: 1,
        justifyContent: 'center'
    },
    header: {
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: Mixins.scale(16),
        position: 'absolute',
        width: '100%'
    }
});
