import React, { useEffect } from 'react';
import {
    TouchableOpacity,
    View,
    Image,
    StyleSheet,
    TextInput,
    KeyboardTypeOptions,
    ReturnKeyTypeOptions
} from 'react-native';
import { Colors, MyText } from 'react-native-xwork';
import { Mixins } from '@mwg-sdk/styles';
import { IMAGES } from '@assets';
import { formattedPrice, priceToText } from '@utils';
import { helper } from '@mwg-kits/common';

export const InputField = React.memo(
    React.forwardRef(
        (
            {
                placeholder,
                value,
                onChange,
                clearValue,
                keyboardType,
                isCurrency,
                isEditable = true,
                isRequired = false,
                multiline = false,
                height = 45,
                returnKeyType = 'done',
                onSubmitEditing,
                msgError = '',
                isClear = false
            }: {
                placeholder: string;
                value: string;
                onChange: (text: string) => void;
                clearValue: () => void;
                keyboardType?: KeyboardTypeOptions;
                isCurrency?: boolean;
                isEditable?: boolean;
                isRequired?: boolean;
                multiline?: boolean;
                height?: number;
                returnKeyType?: ReturnKeyTypeOptions;
                onSubmitEditing?: (isValidate: boolean) => void;
                msgError?: string | null;
                isClear?: boolean;
            },
            ref: React.Ref<TextInput>
        ) => {
            const [textValidate, setTextValidate] = React.useState('');

            useEffect(() => {
                setTextValidate(msgError ?? '');
            }, [msgError]);

            const validateForm = () => {
                if (isRequired && value?.trim()?.length === 0) {
                    setTextValidate(`Vui lòng điền ${placeholder}`);
                    return false;
                }
                setTextValidate('');
                return true;
            };

            const customOnChangeText = (text: string) => {
                if (!isCurrency) return onChange(text);
                return onChange(priceToText(text));
            };

            return (
                <View>
                    <View
                        style={[
                            styles.inputWrapper,
                            {
                                height,
                                borderColor:
                                    textValidate?.length > 0
                                        ? Colors.RED_500
                                        : Colors.NEUTRALS_900
                            }
                        ]}>
                        {!helper.IsNonEmptyString(value) && placeholder && (
                            <MyText
                                text={placeholder}
                                addSize={2}
                                style={{
                                    color: Colors.NEUTRALS_500,
                                    position: 'absolute',
                                    top: 10,
                                    left: 16
                                }}>
                                {isRequired && (
                                    <MyText
                                        text=" *"
                                        style={{ color: Colors.RED_500 }}
                                        category="body.1"
                                    />
                                )}
                            </MyText>
                        )}

                        <TextInput
                            ref={ref}
                            value={
                                value === ''
                                    ? ''
                                    : isCurrency
                                    ? formattedPrice(Number(value), false)
                                    : value
                            }
                            onChangeText={customOnChangeText}
                            style={styles.textInput}
                            keyboardType={keyboardType}
                            editable={isEditable}
                            multiline={multiline}
                            onSubmitEditing={() => {
                                if (onSubmitEditing) {
                                    onSubmitEditing(validateForm());
                                }
                            }}
                            returnKeyType={returnKeyType}
                            blurOnSubmit={true}
                        />
                        {(isEditable || isClear) && value?.length > 0 && (
                            <TouchableOpacity
                                activeOpacity={0.8}
                                onPress={clearValue}>
                                <Image
                                    source={IMAGES.ic_clear}
                                    style={Mixins.scaleImage(20, 20)}
                                    resizeMode="contain"
                                />
                            </TouchableOpacity>
                        )}
                    </View>
                    {textValidate?.length > 0 && (
                        <View style={styles.warning}>
                            <Image
                                source={IMAGES.ic_warning}
                                style={[
                                    Mixins.scaleImage(14, 14),
                                    {
                                        tintColor: Colors.RED_600
                                    }
                                ]}
                                resizeMode="contain"
                            />
                            <MyText
                                text={textValidate}
                                category="chip"
                                style={styles.warningText}
                            />
                        </View>
                    )}
                </View>
            );
        }
    )
);

const styles = StyleSheet.create({
    inputWrapper: {
        borderWidth: 1,
        paddingHorizontal: Mixins.scale(16),
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: Colors.WHITE,
        borderRadius: Mixins.scale(16),
        marginBottom: Mixins.scale(10)
    },
    textInput: {
        flex: 1,
        color: Colors.NEUTRALS_100,
        fontSize: 16
    },
    warning: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: Mixins.scale(10)
    },
    warningText: {
        color: Colors.RED_600,
        marginLeft: Mixins.scale(4)
    }
});
