const HOST_TRANSACTION = `${process.env.HOST_PARTNER}${process.env.PARTNER_SERVICE}/api/accounting/payment/request`;
export const API_SEARCH = `${HOST_TRANSACTION}/search`;
export const API_REVIEW_MULTIPLE = `${HOST_TRANSACTION}/reviewByUserLogin`;
export const API_SEARCH_EXPENSE_CONTENT = `${HOST_TRANSACTION}/getDetailByContent`;
export const API_SEARCH_PAYMENT_REQ = `${HOST_TRANSACTION}/checkDetailByTransactionID`;
export const API_CREATE_TRANSACTION = `${HOST_TRANSACTION}/createByUserLogin`;
export const API_UPDATE_REQUEST_PAY = `${HOST_TRANSACTION}/update`;
export const API_CANCEL_REQUEST = `${HOST_TRANSACTION}/cancel`;
export const API_GET_DETAIL = `${HOST_TRANSACTION}/getDetailByTransactionID`;
export const API_SEARCH_CUSTOMER = `${HOST_TRANSACTION}/searchCustomerByName`;
