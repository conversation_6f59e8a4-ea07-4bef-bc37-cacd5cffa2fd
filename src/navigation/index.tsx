import React, { useEffect } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import {
    RequestPayment,
    ListRequestPayment,
    Menu,
    ApprovePayment,
    DetailRequestPayment
} from '@screens';
import { useDispatch } from 'react-redux';
import * as actionPaymentApproval from '../store/action';

const PaymentApprovalStack = createStackNavigator();

const MainNavigator = (props: any) => {
    const dispatch = useDispatch();

    useEffect(() => {
        dispatch(actionPaymentApproval.getDataFromXwork(props) as any);
    }, []);

    return (
        <PaymentApprovalStack.Navigator
            initialRouteName="Menu"
            screenOptions={{
                headerShown: false
            }}>
            <PaymentApprovalStack.Screen name="Menu" component={Menu} />
            <PaymentApprovalStack.Screen
                name="RequestPayment"
                component={RequestPayment}
            />
            <PaymentApprovalStack.Screen
                name="ListRequestPayment"
                component={ListRequestPayment}
            />
            <PaymentApprovalStack.Screen
                name="ApprovePayment"
                component={ApprovePayment}
            />
            <PaymentApprovalStack.Screen
                name="DetailRequestPayment"
                component={DetailRequestPayment}
            />
        </PaymentApprovalStack.Navigator>
    );
};

export default MainNavigator;
