import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
    TouchableOpacity,
    View,
    Image,
    StyleSheet,
    TextInput,
    FlatList,
    Modal
} from 'react-native';
import { WrapperContainerBase } from '@mwg-kits/components';
import {
    MyText,
    Colors,
    BaseButton,
    BaseBottomSheet,
    NotifyModal
} from 'react-native-xwork';
import { Mixins } from '@mwg-sdk/styles';
import { useDispatch, useSelector } from 'react-redux';
import { RootReducerType } from '../../store/rootReducers';
import { IMAGES } from '@assets';
import * as actionPaymentApproval from '../../store/action';
import { Attachment, SearchRes } from '@types';
import {
    formatTimestamp,
    formattedPrice,
    getCurrentDate,
    getPrevious7Days
} from '@utils';
import { helper } from '@mwg-kits/common';
import { openDocument, ImageView } from '@components';

const ApprovePayment = (props: any) => {
    const { navigation } = props;
    const dispatch = useDispatch();
    const searchState = useSelector(
        (state: RootReducerType) => state.paymentApprovalReducer.search
    );
    const { userInfo } = useSelector(
        (state: RootReducerType) => state.commonReducer.xworkData
    );
    const [isShowModalStatusAprroval, setShowModalStatusApproval] =
        useState(false);
    const [statusApproval, setStatusApproval] = useState('Chưa duyệt');
    const [dataSearch, setDataSearch] = useState<any>({
        keyWord: '',
        isReviewed: 0,
        isShowMore: 0
    });
    const [isShowAlert, setShowAlert] = useState(false);
    const [transactionId, setTransactionId] = useState(['']);
    const [prevSevenDays, setPrevSevenDays] = useState(
        getPrevious7Days(getCurrentDate())
    );
    const [imageId, setImageId] = useState('');

    const actionSearch = useCallback(
        (isShowMore?: number) => {
            dispatch(
                actionPaymentApproval.search({
                    ...dataSearch,
                    isShowMore: isShowMore ? isShowMore : 0
                }) as any
            );
        },
        [dataSearch, dispatch]
    );

    const showMore = useCallback(() => {
        setDataSearch((prev: any) => ({
            ...prev,
            isShowMore: prev.isShowMore + 1
        }));
        setPrevSevenDays((prev) => getPrevious7Days(prev));
        actionSearch(dataSearch.isShowMore + 1);
    }, [actionSearch]);

    console.log('prev sevent days: ', prevSevenDays);

    const approveRequest = useCallback(() => {
        dispatch(
            actionPaymentApproval.reviewMulti(
                transactionId,
                actionSearch
            ) as any
        );
        setShowAlert(false);
    }, [transactionId, dispatch]);

    const componentsLeft = () => {
        return (
            <View style={styles.componentLeft}>
                <TouchableOpacity
                    hitSlop={styles.btnArrow}
                    onPress={() => navigation.goBack()}>
                    <Image
                        resizeMode="contain"
                        source={{
                            uri: 'ic_direct'
                        }}
                        style={[Mixins.scaleImage(32, 32), styles.iconLeft]}
                    />
                </TouchableOpacity>
                <MyText
                    text={'Duyệt yêu cầu thanh toán'}
                    addSize={4}
                    typeFont="bold"
                    numberOfLines={1}
                    style={styles.headTitle}
                />
            </View>
        );
    };

    const BottomSheetStatusApproval = useMemo(() => {
        const statusApproval = [
            {
                status: 'Tất cả',
                statusId: -1
            },
            {
                status: 'Đã duyệt',
                statusId: 1
            },
            {
                status: 'Chưa duyệt',
                statusId: 0
            }
        ];
        return (
            <BaseBottomSheet
                isModalVisible={isShowModalStatusAprroval}
                toggleModal={() => setShowModalStatusApproval(false)}
                titleName="Trạng thái yêu cầu"
                isHeight={0.3}>
                {statusApproval.map((item, index) => {
                    return (
                        <TouchableOpacity
                            onPress={() => {
                                setShowModalStatusApproval(false);
                                setStatusApproval(item.status);
                                setDataSearch({
                                    ...dataSearch,
                                    isReviewed: item.statusId
                                });
                            }}
                            key={index}>
                            <MyText
                                text={item.status}
                                category="regular.body"
                                style={{ color: Colors.NEUTRALS_200 }}
                            />
                            <View style={styles.lineSeparator} />
                        </TouchableOpacity>
                    );
                })}
            </BaseBottomSheet>
        );
    }, [isShowModalStatusAprroval, dataSearch]);

    const renderItem = ({
        item,
        index
    }: {
        item: SearchRes;
        index: number;
    }) => {
        console.log('get item approve: ', item);
        return (
            <View style={styles.itemContainer}>
                <View style={styles.commonRow}>
                    <MyText
                        text={`${formatTimestamp(item.dateCreate)}`}
                        category="regular.caption.1"
                        style={{ color: Colors.NEUTRALS_500 }}
                    />
                    <View
                        style={[
                            styles.statusContainer,
                            {
                                backgroundColor:
                                    item.isReview === 0
                                        ? Colors.BRAND_1000
                                        : Colors.GREEN_950
                            }
                        ]}>
                        <MyText
                            text={
                                item.isReview === 0 ? `Chờ duyệt` : 'Đã duyệt'
                            }
                            category="regular.caption.1"
                            style={{
                                color:
                                    item.isReview === 0
                                        ? Colors.BRAND_500
                                        : Colors.GREEN_600
                            }}
                        />
                    </View>
                </View>
                <View style={{ marginTop: Mixins.scale(4) }}>
                    <MyText
                        text={`Mã phiếu: `}
                        category="regular.caption.1"
                        style={{ color: Colors.NEUTRALS_500 }}>
                        <MyText
                            text={item.transactionID}
                            category="bold.caption.1"
                            style={{ color: Colors.NEUTRALS_500 }}
                        />
                    </MyText>
                </View>
                <View style={{ marginTop: Mixins.scale(4) }}>
                    <MyText
                        text={`Người yêu cầu: `}
                        category="regular.caption.1"
                        style={{ color: Colors.NEUTRALS_500 }}>
                        <MyText
                            text={
                                item?.userRequest
                                    ? `${item?.userRequest} - ${item.requestUserName}`
                                    : item.requestUserName
                            }
                            category="bold.caption.1"
                            style={{ color: Colors.NEUTRALS_500 }}
                        />
                    </MyText>
                </View>
                {item.content && (
                    <View style={{ marginTop: Mixins.scale(4) }}>
                        <MyText
                            text={`Nội dung: `}
                            category="regular.caption.1"
                            style={{ color: Colors.NEUTRALS_500 }}
                        />
                        <View style={styles.contentContainer}>
                            <MyText
                                text={item.content}
                                category="regular.body"
                                style={{ color: Colors.NEUTRALS_500 }}
                            />
                        </View>
                    </View>
                )}

                {helper.IsNonEmptyArray(
                    item.transactionAttachmentEntityList
                ) && (
                    <RenderListAttachment
                        listAttachment={item.transactionAttachmentEntityList}
                    />
                )}
                {Boolean(item.isInitialReview) &&
                    item.userInitialReviewName &&
                    item.initialReviewedDate && (
                        <MyText
                            text={`Người duyệt 1: ${
                                item?.userInitialReview
                            } - ${
                                item.userInitialReviewName
                            } lúc ${formatTimestamp(
                                Number(item.initialReviewedDate)
                            )}`}
                            category="regular.caption.1"
                            style={styles.approveText}
                        />
                    )}
                {Boolean(item.isReview) &&
                    item.userReviewName &&
                    item.reviewedDate && (
                        <MyText
                            text={`Người duyệt 2: ${item?.userReview} - ${
                                item.userReviewName
                            } lúc ${formatTimestamp(
                                Number(item.reviewedDate)
                            )}`}
                            category="regular.caption.1"
                            style={styles.approveText}
                        />
                    )}
                {Boolean(item.isReview) && item?.paymentDate && (
                    <MyText
                        text={`Hạn thanh toán:  ${formatTimestamp(
                            Number(item?.paymentDate)
                        )}`}
                        category="regular.caption.1"
                        style={styles.priceText}
                    />
                )}
                <View style={styles.priceWrapper}>
                    <MyText
                        text={`Số tiền: `}
                        category="regular.caption.1"
                        style={styles.priceText}>
                        <MyText
                            text={`${formattedPrice(item.totalAmount)}`}
                            category="bold.body.2"
                            style={styles.priceText2}
                        />
                    </MyText>
                    {item.isReview === 0 && (
                        <TouchableOpacity
                            disabled={
                                item.userInitialReview === userInfo?.userName
                                    ? true
                                    : false
                            }
                            key={index}
                            onPress={() => {
                                setShowAlert(true);
                                setTransactionId([item.transactionID?.trim()]);
                            }}
                            style={[
                                styles.btnCancel,
                                {
                                    opacity:
                                        item.userInitialReview ===
                                        userInfo?.userName
                                            ? 0.6
                                            : 1
                                }
                            ]}>
                            <MyText
                                text={'Duyệt'}
                                category="button.small"
                                style={{
                                    color: Colors.WHITE
                                }}
                            />
                        </TouchableOpacity>
                    )}
                </View>
            </View>
        );
    };

    const RenderListAttachment = useCallback(
        ({ listAttachment }: { listAttachment: Attachment[] }) => {
            const supportedImageTypes = [
                'jpeg',
                'png',
                'jpg',
                'webp',
                'svg',
                'gif',
                'bmp'
            ];
            return (
                <View
                    style={{
                        flexDirection: 'row',
                        marginTop: Mixins.scale(16)
                    }}>
                    {listAttachment?.map((item, index) => {
                        return (
                            <View style={{ marginRight: Mixins.scale(8) }}>
                                {supportedImageTypes.some((type) =>
                                    item.fileType?.toLowerCase()?.includes(type)
                                ) ? (
                                    <TouchableOpacity
                                        key={index}
                                        onPress={() =>
                                            setImageId(item.attachmentID)
                                        }
                                        style={styles.imgWrapper}>
                                        <Image
                                            source={{ uri: item.fileURL }}
                                            style={[Mixins.scaleImage(72, 72)]}
                                            resizeMode="cover"
                                        />
                                    </TouchableOpacity>
                                ) : (
                                    <TouchableOpacity
                                        key={index}
                                        onPress={() => {
                                            openDocument(
                                                item.fileURL,
                                                item.fileType
                                                    .replace('.', '')
                                                    .toLowerCase()
                                            );
                                        }}
                                        style={styles.fileWrapper}>
                                        <Image
                                            source={IMAGES.ic_attachment}
                                            style={[
                                                Mixins.scaleImage(24, 24),
                                                {
                                                    marginTop: Mixins.scale(8)
                                                }
                                            ]}
                                            resizeMode="contain"
                                        />
                                        <MyText
                                            text={item.attachmentFileName}
                                            addSize={-3}
                                            numberOfLines={2}
                                            ellipsizeMode="tail"
                                            style={{
                                                marginTop: Mixins.scale(8),
                                                color: Colors.BRAND_600
                                            }}
                                        />
                                    </TouchableOpacity>
                                )}
                                <Modal
                                    animationType="none"
                                    transparent={true}
                                    visible={item.attachmentID === imageId}
                                    onRequestClose={() => setImageId('')}>
                                    <ImageView
                                        imgList={[{ url: item.fileURL }]}
                                        indexImage={0}
                                        onPressClose={() => setImageId('')}
                                    />
                                </Modal>
                            </View>
                        );
                    })}
                </View>
            );
        },
        [imageId]
    );

    return (
        <View style={styles.container}>
            <WrapperContainerBase componentsLeft={componentsLeft}>
                <View style={styles.wrapper}>
                    <View style={styles.searchContainer}>
                        <View style={styles.searchInputContainer}>
                            <Image
                                source={IMAGES.ic_search}
                                style={Mixins.scaleImage(20, 20)}
                                resizeMode="contain"
                            />
                            <TextInput
                                placeholder="Nhập mã phiếu yêu cầu/ Nội dung/ Người yêu cầu"
                                placeholderTextColor={Colors.NEUTRALS_500}
                                value={dataSearch.keyWord}
                                onChangeText={(text) => {
                                    setDataSearch({
                                        ...dataSearch,
                                        keyWord: text
                                    });
                                }}
                                style={styles.textInput}
                            />
                        </View>

                        {dataSearch?.keyWord?.trim()?.length > 0 && (
                            <TouchableOpacity
                                onPress={() =>
                                    setDataSearch({
                                        ...dataSearch,
                                        keyWord: ''
                                    })
                                }
                                activeOpacity={0.8}
                                style={styles.clearButton}>
                                <Image
                                    source={IMAGES.ic_clear}
                                    style={Mixins.scaleImage(20, 20)}
                                    resizeMode="contain"
                                />
                            </TouchableOpacity>
                        )}
                    </View>
                    <View style={{ marginTop: Mixins.scale(16) }}>
                        <MyText
                            text="Trạng thái yêu cầu"
                            category="regular.sub.title.2"
                            style={{ color: Colors.NEUTRALS_100 }}
                        />
                        <TouchableOpacity
                            onPress={() => setShowModalStatusApproval(true)}
                            style={styles.status}>
                            <MyText
                                text={statusApproval}
                                category="regular.body"
                                style={{ color: Colors.NEUTRALS_100 }}
                            />
                            <Image
                                source={IMAGES.ic_arrow_down}
                                style={Mixins.scaleImage(20, 20)}
                                resizeMode="contain"
                            />
                        </TouchableOpacity>
                    </View>
                    <BaseButton
                        onPress={() => actionSearch()}
                        text={'Tìm kiếm'}
                        buttonStyle={styles.button}
                    />
                    <FlatList
                        data={searchState.data}
                        renderItem={renderItem}
                        keyExtractor={(item, index) => index.toString()}
                        showsVerticalScrollIndicator={false}
                        scrollEnabled={helper.IsNonEmptyArray(searchState.data)}
                        ListFooterComponent={() => {
                            return (
                                <TouchableOpacity
                                    onPress={showMore}
                                    style={styles.btnShowMore}>
                                    <Image
                                        source={{ uri: 'ic_left_arrow' }}
                                        style={[
                                            Mixins.scaleImage(20, 20),
                                            {
                                                tintColor: Colors.BRAND_600
                                            }
                                        ]}
                                        resizeMode="contain"
                                    />
                                    <MyText
                                        text={`Xem thêm ${prevSevenDays}`}
                                        category="button.small"
                                        style={styles.textShowMore}
                                    />
                                </TouchableOpacity>
                            );
                        }}
                    />
                    <NotifyModal
                        isVisible={isShowAlert}
                        title={'Xác nhận duyệt yêu cầu?'}
                        typeNotify="option"
                        typeIcon="info"
                        content={'Bạn có duyệt yêu cầu này không?'}
                        onConfirm={approveRequest}
                        onCancel={() => setShowAlert(false)}
                        titleCancel="Không"
                    />
                    {BottomSheetStatusApproval}
                </View>
            </WrapperContainerBase>
        </View>
    );
};
const styles = StyleSheet.create({
    container: {
        backgroundColor: Colors.WHITE,
        flex: 1
    },
    wrapper: {
        flex: 1,
        backgroundColor: Colors.WHITE,
        paddingHorizontal: Mixins.scale(16),
        paddingTop: Mixins.scale(16)
    },
    headTitle: {
        color: Colors.NEUTRALS_200,
        maxWidth: Mixins.scale(300)
    },
    iconLeft: {
        alignSelf: 'center',
        marginRight: Mixins.scale(8),
        tintColor: Colors.BRAND_600,
        transform: [{ rotate: '180deg' }]
    },
    btnArrow: {
        top: 20,
        left: 20,
        right: 20,
        bottom: 20
    },
    componentLeft: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    searchContainer: {
        backgroundColor: Colors.NEUTRALS_950,
        borderRadius: Mixins.scale(16),
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: Mixins.scale(16),
        height: Mixins.scale(46)
    },
    searchInputContainer: {
        flex: 8,
        flexDirection: 'row',
        alignItems: 'center'
    },
    textInput: {
        backgroundColor: Colors.NEUTRALS_950,
        flex: 1,
        paddingHorizontal: Mixins.scale(8)
    },
    clearButton: {
        flex: 0.5
    },
    button: {
        marginTop: Mixins.scale(16),
        backgroundColor: Colors.BADGE_GHOST_BLUE_TEXT,
        height: Mixins.scale(46)
    },
    status: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: Mixins.scale(8),
        borderWidth: 1,
        borderRadius: Mixins.scale(16),
        borderColor: Colors.NEUTRALS_900,
        paddingVertical: Mixins.scale(10),
        paddingHorizontal: Mixins.scale(16)
    },
    lineSeparator: {
        height: 1,
        backgroundColor: Colors.NEUTRALS_900,
        marginVertical: Mixins.scale(16)
    },
    btnCancel: {
        paddingHorizontal: Mixins.scale(32),
        height: Mixins.scale(40),
        marginTop: Mixins.scale(8),
        backgroundColor: Colors.BADGE_GHOST_BLUE_TEXT,
        borderRadius: Mixins.scale(16),
        justifyContent: 'center',
        alignItems: 'center'
    },
    btnCancelDisabled: {
        paddingHorizontal: Mixins.scale(32),
        height: Mixins.scale(40),
        marginTop: Mixins.scale(8),
        backgroundColor: Colors.NEUTRALS_950,
        borderRadius: Mixins.scale(16),
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: Colors.NEUTRALS_900
    },
    itemContainer: {
        backgroundColor: Colors.NEUTRALS_950,
        borderRadius: Mixins.scale(10),
        paddingVertical: Mixins.scale(14),
        paddingHorizontal: Mixins.scale(16),
        marginTop: Mixins.scale(16),
        flex: 1
    },
    statusContainer: {
        paddingHorizontal: Mixins.scale(6),
        height: Mixins.scale(22),
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: Mixins.scale(6)
    },
    contentContainer: {
        borderWidth: 1,
        borderColor: Colors.NEUTRALS_900,
        borderRadius: Mixins.scale(16),
        backgroundColor: Colors.WHITE,
        padding: Mixins.scale(16)
    },
    priceWrapper: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: Mixins.scale(4)
    },
    priceText: {
        color: Colors.NEUTRALS_500,
        marginTop: Mixins.scale(8)
    },
    priceText2: {
        color: Colors.RED_500,
        marginTop: Mixins.scale(8)
    },
    approveText: {
        color: Colors.BRAND_600,
        marginTop: Mixins.scale(12)
    },
    commonRow: {
        flexDirection: 'row',
        justifyContent: 'space-between'
    },
    textShowMore: {
        color: Colors.BRAND_600,
        marginLeft: Mixins.scale(8)
    },
    btnShowMore: {
        flexDirection: 'row',
        alignItems: 'center',
        margin: Mixins.scale(16)
    },
    imgWrapper: {
        height: Mixins.scale(72),
        width: Mixins.scale(72),
        backgroundColor: Colors.NEUTRALS_950,
        borderRadius: Mixins.scale(10),
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: Mixins.scale(8),
        overflow: 'hidden'
    },
    fileWrapper: {
        height: Mixins.scale(72),
        width: Mixins.scale(72),
        backgroundColor: Colors.WHITE,
        borderRadius: Mixins.scale(10),
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: Mixins.scale(8),
        overflow: 'hidden',
        padding: Mixins.scale(8)
    }
});
export default React.memo(ApprovePayment);
