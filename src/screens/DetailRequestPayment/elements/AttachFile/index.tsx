import React, { useEffect, useState } from 'react';
import { View, Image, StyleSheet, TouchableOpacity, Modal } from 'react-native';
import { MyText, Colors } from 'react-native-xwork';
import { Mixins } from '@mwg-sdk/styles';
import { IMAGES } from '@assets';
import { PermissionCamera, PermissionPhoto, ImageView } from '@components';
import { Attachment } from '@types';

const AttachFile = ({
    onPressListAttachment,
    listAttachment,
    isDisabled
}: {
    onPressListAttachment: (item: Attachment[]) => void;
    listAttachment: Attachment[] | undefined;
    isDisabled: boolean;
}) => {
    const initialImageState: Attachment = {
        attachmentID: '',
        transactionID: '',
        attachmentName: null,
        description: '',
        fileType: '',
        fileURL: '',
        attachmentFileName: '',
        dateCreate: null,
        status: 1 // add image to list set status: 1
    };

    const [listImage, setListImage] = useState<Attachment[]>(
        Array(3).fill(initialImageState)
    );
    const [imageId, setImageId] = useState<any>(null);

    useEffect(() => {
        if (listAttachment && listAttachment?.length > 0) {
            if (!isDisabled) {
                const updatedList = [...listAttachment];
                const itemsToAdd = 3 - updatedList?.length;
                if (itemsToAdd > 0) {
                    const newItems = Array(itemsToAdd)
                        .fill(initialImageState)
                        .map((_) => ({
                            ...initialImageState,
                            transactionID: listAttachment[0]?.transactionID,
                            description: listAttachment[0]?.description
                        }));
                    updatedList.push(...newItems);
                }
                setListImage(updatedList);
            } else {
                setListImage(listAttachment);
            }
        }
    }, [listAttachment]);

    useEffect(() => {
        if (listImage) {
            onPressListAttachment(listImage);
        }
    }, [listImage]);

    console.log('get list image: ', listImage);

    const updateImageList = (response: any, index: number) => {
        const updatedImages = [...listImage];
        if (Array.isArray(response)) {
            response.slice(0, 3).forEach((item: any, idx: number) => {
                updatedImages[index + idx] = {
                    ...updatedImages[index + idx],
                    fileURL: `data:@file/jpeg;base64,${item.base64}`
                };
            });
        } else if (typeof response === 'object' && response.base64) {
            updatedImages[index] = {
                ...updatedImages[index],
                fileURL: `data:@file/jpeg;base64,${response.base64}`
            };
        }
        setListImage(updatedImages.slice(0, 3));
    };

    const checkPermissions = (isOpenCamera = false, index: number) => {
        const permissionCallback = isOpenCamera
            ? PermissionCamera
            : PermissionPhoto;
        permissionCallback((response: any, error: any) => {
            if (response) {
                updateImageList(response, index);
            }
            if (error) {
                console.log('errorPer==', error.message);
            }
        });
    };

    const removeImage = (item: Attachment) => {
        const index = listImage.indexOf(item);
        if (index !== -1) {
            const updatedImages = [...listImage];
            updatedImages[index].status = -1; // remove image from list set status: -1
            setListImage(updatedImages);
        }
    };

    console.log('get list image: ', listImage);
    return (
        <View
            style={{
                marginTop: Mixins.scale(20)
            }}>
            <MyText
                text="Thêm file đính kèm"
                category="bold.sub.title.2"
                style={{ color: Colors.NEUTRALS_100 }}
            />
            <MyText
                text="Tối đa 3 file"
                category="regular.caption.1"
                style={{ color: Colors.NEUTRALS_500 }}
            />
            <View
                style={{
                    marginTop: Mixins.scale(8),
                    flexDirection: 'row'
                }}>
                {listImage.map((item, index) => {
                    return (
                        <View>
                            <TouchableOpacity
                                disabled={isDisabled}
                                key={index}
                                onPress={() => {
                                    if (item.attachmentID) {
                                        setImageId(item.attachmentID);
                                    } else {
                                        checkPermissions(false, index);
                                    }
                                }}
                                style={styles.image}>
                                {item.fileURL?.length > 0 &&
                                item.status !== -1 ? (
                                    <View>
                                        <Image
                                            source={{
                                                uri: `${item.fileURL}`
                                            }}
                                            style={[Mixins.scaleImage(72, 72)]}
                                            resizeMode="cover"
                                        />
                                    </View>
                                ) : (
                                    <View style={styles.childImage}>
                                        <Image
                                            source={IMAGES.ic_library}
                                            style={Mixins.scaleImage(24, 24)}
                                            resizeMode="contain"
                                        />
                                        <MyText
                                            text="Thêm ảnh"
                                            category="tag.item"
                                            style={{
                                                color: Colors.NEUTRALS_700,
                                                marginTop: Mixins.scale(4)
                                            }}
                                        />
                                    </View>
                                )}
                            </TouchableOpacity>
                            {item.fileURL?.length > 0 &&
                                item.status !== -1 &&
                                !isDisabled && (
                                    <TouchableOpacity
                                        key={index}
                                        onPress={() => removeImage(item)}
                                        style={styles.iconDelete}>
                                        <Image
                                            source={IMAGES.ic_clear}
                                            style={[Mixins.scaleImage(16, 16)]}
                                            resizeMode="contain"
                                        />
                                    </TouchableOpacity>
                                )}
                            <Modal
                                animationType="none"
                                transparent={true}
                                visible={item.attachmentID === imageId}
                                onRequestClose={() => setImageId(null)}>
                                <ImageView
                                    imgList={[{ url: item.fileURL }]}
                                    indexImage={0}
                                    onPressClose={() => setImageId(null)}
                                />
                            </Modal>
                        </View>
                    );
                })}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    iconDelete: {
        zIndex: 1,
        position: 'absolute',
        left: Mixins.scale(60),
        bottom: Mixins.scale(62)
    },
    image: {
        height: Mixins.scale(72),
        width: Mixins.scale(72),
        backgroundColor: Colors.NEUTRALS_950,
        borderRadius: Mixins.scale(10),
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: Mixins.scale(8),
        overflow: 'hidden'
    },
    childImage: {
        alignItems: 'center',
        justifyContent: 'center'
    }
});

export default React.memo(AttachFile);
