import React, { useCallback, useEffect, useState } from 'react';
import {
    TouchableOpacity,
    View,
    Image,
    StyleSheet,
    Platform
} from 'react-native';
import { MyText, Colors, NotifyModal } from 'react-native-xwork';
import { WrapperContainerBase } from '@mwg-kits/components';
import { Mixins } from '@mwg-sdk/styles';
import {
    SelectContentPayment,
    ButtonRequest,
    InfoContentPayment,
    AttachFile
} from './elements';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import * as actionPaymentApproval from '../../store/action';
import { useDispatch, useSelector } from 'react-redux';
import {
    Attachment,
    DetailEntity,
    SearchExpenseContent,
    TransactionDetailEntity
} from '@types';
import { RootReducerType } from '../../store/rootReducers';
import Toast from 'react-native-toast-message';

const { translate } = (global as any).props.getTranslateConfig();

const DetailRequestPayment = (props: any) => {
    const { navigation, route } = props;
    const { transactionId, actionSearch } = route?.params;
    const dispatch = useDispatch();
    const getDetailPaymentState = useSelector(
        (state: RootReducerType) =>
            state.paymentApprovalReducer.getDetailPayment
    );
    const searchExpenseContentState = useSelector(
        (state: RootReducerType) =>
            state.paymentApprovalReducer.searchExpenseContent
    );
    const transactionObjectData = getDetailPaymentState.data?.transactionObject;
    const [isShowModalCancel, setShowModalCancel] = useState(false);
    const [isShowModalUpdate, setShowModalUpdate] = useState(false);
    const [selectedItem, setSelectedItem] = useState<
        SearchExpenseContent | any
    >(null);
    const [fileAttachment, setFileAttachment] = useState<Attachment[]>([]);
    const [transactionObject, setTransactionObject] = useState<{
        transactionDetailEntities: TransactionDetailEntity[];
        totalAmount: number;
    }>({ transactionDetailEntities: [], totalAmount: 0 });

    useEffect(() => {
        actionGetDetail();
    }, [transactionId]);

    useEffect(() => {
        if (transactionObjectData?.expenseContentName) {
            actionSearchExpenseContent();
        }
    }, [transactionObjectData?.expenseContentName]);

    const actionGetDetail = useCallback(() => {
        dispatch(actionPaymentApproval.getDetailPayment(transactionId) as any);
    }, [transactionId]);

    const actionSearchExpenseContent = useCallback(() => {
        const key = transactionObjectData?.expenseContentName;
        dispatch(actionPaymentApproval.searchExpenseContent(key) as any);
    }, [transactionObjectData?.expenseContentName]);

    const actionCancel = useCallback(() => {
        const body = {
            transactionList: [
                {
                    transactionID: transactionObjectData?.transactionId ?? '',
                    transactionTypeID:
                        transactionObjectData?.transactionTypeId ?? -1,
                    isLock: transactionObjectData?.isLock ?? -1,
                    isDelete: transactionObjectData?.isDelete ?? -1,
                    isReview: transactionObjectData?.isReview ?? -1,
                    isInitialReview:
                        transactionObjectData?.isInitialReview ?? -1
                }
            ]
        };
        console.log('body cancel in detail: ', body);
        setShowModalCancel(false);
        dispatch(actionPaymentApproval.cancelRequest(body, navigation) as any);
    }, [transactionObjectData]);

    const actionUpdate = useCallback(() => {
        const isValidatedContent = checkEnableBtnRequest();
        const body = {
            transactionObject: {
                ...transactionObjectData,
                expenseContentId: selectedItem.expenseContentId,
                expenseContentName: selectedItem.expenseContentName,
                totalAmount: transactionObject.totalAmount,
                transactionDetailEntities: updateTransactionDetails({
                    transactionDetailEntities:
                        transactionObjectData?.transactionDetailEntities,
                    listUpdate: transactionObject.transactionDetailEntities
                }),
                transactionAttachmentEntities: fileAttachment
            },
            expenseContentId: selectedItem.expenseContentId,
            expenseContentName: selectedItem.expenseContentName
        };
        console.log('body update: ', body);
        setShowModalUpdate(false);
        if (isValidatedContent) {
            dispatch(
                actionPaymentApproval.updateRequestPay(
                    body,
                    navigation,
                    actionSearch
                ) as any
            );
        }
    }, [
        fileAttachment,
        selectedItem,
        transactionObject,
        transactionObjectData,
        actionSearch
    ]);

    console.log('select items updated: ', selectedItem);

    const updateTransactionDetails = ({
        transactionDetailEntities = [],
        listUpdate
    }: {
        transactionDetailEntities: DetailEntity[] | any;
        listUpdate: TransactionDetailEntity[];
    }) => {
        listUpdate.forEach((item: any, index: number) => {
            if (transactionDetailEntities[index] && item.status !== -1) {
                Object.keys(item).forEach((key) => {
                    if (transactionDetailEntities[index][key] !== item[key]) {
                        transactionDetailEntities[index][key] = item[key];
                        transactionDetailEntities[index].status = 2;
                    }
                });
            }
        });
        const newListItem = listUpdate.filter((e) => e.status === 1);
        return [...transactionDetailEntities, ...newListItem];
    };

    const componentsLeft = () => {
        return (
            <View style={styles.componentLeft}>
                <TouchableOpacity
                    hitSlop={styles.btnArrow}
                    onPress={() => navigation.goBack()}>
                    <Image
                        resizeMode="contain"
                        source={{
                            uri: 'ic_direct'
                        }}
                        style={[Mixins.scaleImage(32, 32), styles.iconLeft]}
                    />
                </TouchableOpacity>
                <MyText
                    text={'Chi tiết yêu cầu thanh toán'}
                    addSize={4}
                    typeFont="bold"
                    numberOfLines={1}
                    style={styles.headTitle}
                />
            </View>
        );
    };

    const Description = useCallback(() => {
        if (selectedItem) {
            return (
                <View style={{ marginTop: Mixins.scale(20) }}>
                    <MyText
                        text="Mô tả:"
                        category="regular.body.2"
                        style={{
                            color: Colors.NEUTRALS_500,
                            textDecorationLine: 'underline'
                        }}
                    />
                    <MyText
                        text={selectedItem.description}
                        category="regular.body.2"
                        style={styles.description}
                    />
                </View>
            );
        }
        return null;
    }, [selectedItem]);

    const checkEnableBtnRequest = () => {
        const validateForm = transactionObject.transactionDetailEntities.every(
            (entity) => {
                const {
                    totalAmount,
                    invoiceID,
                    invoiceSymbol,
                    denominator,
                    status
                } = entity;
                const isRequired = Boolean(selectedItem?.isRequiredInvoice);

                if (status === -1) return true;

                return (
                    totalAmount > 0 &&
                    totalAmount < 1000000000 &&
                    (!isRequired ||
                        (invoiceID?.length > 0 &&
                            invoiceSymbol?.length > 0 &&
                            denominator?.length > 0))
                );
            }
        );
        return selectedItem && transactionObject.totalAmount && validateForm
            ? true
            : false;
    };

    return (
        <View style={styles.container}>
            <WrapperContainerBase
                componentsLeft={componentsLeft}
                isSuccess={getDetailPaymentState?.isSuccess}
                isError={getDetailPaymentState?.isError}
                messageError={getDetailPaymentState?.msgError}
                isLoading={getDetailPaymentState?.isFetching}
                actionRetry={actionGetDetail}
                isErrorIcon={{ uri: 'ic_error' }}
                buttonRetry={translate('retry')}>
                <KeyboardAwareScrollView
                    behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                    contentContainerStyle={styles.scrollView}
                    showsVerticalScrollIndicator={false}
                    keyboardShouldPersistTaps="handled">
                    <SelectContentPayment
                        onSelected={setSelectedItem}
                        initialContent={
                            searchExpenseContentState.data &&
                            searchExpenseContentState?.data[0]
                        }
                        isDisabled={Boolean(transactionObjectData?.isReview)}
                    />
                    <Description />
                    <InfoContentPayment
                        onContentRequest={setTransactionObject}
                        selectedItem={selectedItem}
                        transactionDetailEntities={
                            transactionObjectData?.transactionDetailEntities
                        }
                        isDisabled={Boolean(transactionObjectData?.isReview)}
                    />
                    <AttachFile
                        onPressListAttachment={(item) => {
                            const newItem = item.filter(
                                (e) => e.fileURL?.length > 0
                            );
                            console.log('get newItem: ', newItem);
                            setFileAttachment(newItem);
                        }}
                        listAttachment={
                            transactionObjectData?.transactionAttachmentEntities
                        }
                        isDisabled={Boolean(transactionObjectData?.isReview)}
                    />
                </KeyboardAwareScrollView>

                <ButtonRequest
                    price={transactionObject.totalAmount}
                    onPressCancel={() => navigation.goBack()}
                    isHideBtn={Boolean(transactionObjectData?.isReview)}
                    onPressUpdate={() => setShowModalUpdate(true)}
                />
                <NotifyModal
                    isVisible={isShowModalCancel}
                    title={'Xác nhận xóa yêu cầu?'}
                    typeNotify="option"
                    typeIcon="error"
                    content={'Bạn có xác nhận xóa yêu cầu?'}
                    onConfirm={actionCancel}
                    onCancel={() => setShowModalCancel(false)}
                    titleCancel="Hủy yêu cầu xóa"
                    titleConfirm="Xác nhận"
                />
                <NotifyModal
                    isVisible={isShowModalUpdate}
                    title={'Xác nhận cập nhật?'}
                    typeNotify="option"
                    typeIcon="success"
                    content={'Bạn có xác nhận cập nhật phiếu?'}
                    onConfirm={actionUpdate}
                    onCancel={() => setShowModalUpdate(false)}
                    titleCancel="Hủy cập nhật"
                    titleConfirm="Xác nhận"
                />
            </WrapperContainerBase>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: Colors.WHITE,
        flex: 1
    },
    scrollView: {
        paddingTop: Mixins.scale(16),
        paddingHorizontal: Mixins.scale(16),
        flexGrow: 1,
        paddingBottom: Mixins.scale(24)
    },
    headTitle: {
        color: Colors.NEUTRALS_200,
        maxWidth: Mixins.scale(300)
    },
    iconLeft: {
        alignSelf: 'center',
        marginRight: Mixins.scale(8),
        tintColor: Colors.BRAND_600,
        transform: [{ rotate: '180deg' }]
    },
    btnArrow: {
        top: 20,
        left: 20,
        right: 20,
        bottom: 20
    },
    componentLeft: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    description: {
        color: Colors.NEUTRALS_500,
        marginTop: Mixins.scale(4)
    }
});
export default React.memo(DetailRequestPayment);
