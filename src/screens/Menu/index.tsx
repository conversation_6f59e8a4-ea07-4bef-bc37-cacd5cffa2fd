import React from 'react';
import { TouchableOpacity, View, Image, StyleSheet } from 'react-native';
import { WrapperContainerBase } from '@mwg-kits/components';
import { MyText, Colors } from 'react-native-xwork';
import { Mixins } from '@mwg-sdk/styles';
import { useSelector } from 'react-redux';
import { RootReducerType } from '../../store/rootReducers';
import { IMAGES } from '@assets';

const Menu = (props: any) => {
    const { navigation } = props;
    const { navigation: globalNavigation } = useSelector(
        (state: RootReducerType) => state.commonReducer.xworkData
    );

    const componentsLeft = () => {
        return (
            <View style={styles.componentLeft}>
                <TouchableOpacity
                    hitSlop={styles.btnArrow}
                    onPress={() => globalNavigation.goBack()}>
                    <Image
                        resizeMode="contain"
                        source={{
                            uri: 'ic_direct'
                        }}
                        style={[Mixins.scaleImage(32, 32), styles.iconLeft]}
                    />
                </TouchableOpacity>
                <MyText
                    text={'Yêu cầu thanh toán'}
                    addSize={4}
                    typeFont="bold"
                    numberOfLines={1}
                    style={styles.headTitle}
                />
            </View>
        );
    };

    return (
        <View style={styles.container}>
            <WrapperContainerBase componentsLeft={componentsLeft}>
                <View style={styles.wrapper}>
                    <TouchableOpacity
                        onPress={() => navigation.navigate('RequestPayment')}
                        style={styles.createRequestBtn}>
                        <Image
                            source={IMAGES.ic_edit_square}
                            style={Mixins.scaleImage(28, 28)}
                            resizeMode="contain"
                        />
                        <MyText
                            text="TẠO YÊU CẦU THANH TOÁN"
                            addSize={1}
                            typeFont="medium"
                            style={styles.textCreate}
                        />
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={() =>
                            navigation.navigate('ListRequestPayment')
                        }
                        style={styles.listRequestBtn}>
                        <Image
                            source={IMAGES.ic_clipboard}
                            style={Mixins.scaleImage(28, 28)}
                            resizeMode="contain"
                        />
                        <MyText
                            text="DANH SÁCH YÊU CẦU THANH TOÁN"
                            addSize={1}
                            typeFont="medium"
                            style={styles.textList}
                        />
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={() => navigation.navigate('ApprovePayment')}
                        style={styles.approveRequestBtn}>
                        <Image
                            source={IMAGES.ic_task_list}
                            style={Mixins.scaleImage(28, 28)}
                            resizeMode="contain"
                        />
                        <MyText
                            text="DUYỆT YÊU CẦU THANH TOÁN"
                            addSize={1}
                            typeFont="medium"
                            style={styles.textApprove}
                        />
                    </TouchableOpacity>
                </View>
            </WrapperContainerBase>
        </View>
    );
};
const styles = StyleSheet.create({
    container: {
        backgroundColor: Colors.WHITE,
        flex: 1
    },
    wrapper: {
        flex: 1,
        backgroundColor: Colors.WHITE,
        paddingHorizontal: Mixins.scale(16),
        paddingTop: Mixins.scale(30)
    },
    headTitle: {
        color: Colors.NEUTRALS_200,
        maxWidth: Mixins.scale(300)
    },
    iconLeft: {
        alignSelf: 'center',
        marginRight: Mixins.scale(8),
        tintColor: Colors.BRAND_600,
        transform: [{ rotate: '180deg' }]
    },
    btnArrow: {
        top: 20,
        left: 20,
        right: 20,
        bottom: 20
    },
    componentLeft: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    createRequestBtn: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: Mixins.scale(16),
        padding: Mixins.scale(16),
        backgroundColor: '#F0FAFE',
        borderColor: '#D9F2FC'
    },
    textCreate: {
        color: '#016FA6',
        marginLeft: Mixins.scale(10)
    },
    listRequestBtn: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: Mixins.scale(16),
        padding: Mixins.scale(16),
        backgroundColor: '#E1FCF8',
        borderColor: '#CBFBF4',
        marginTop: Mixins.scale(16)
    },
    textList: {
        color: '#018A8E',
        marginLeft: Mixins.scale(10)
    },
    approveRequestBtn: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: Mixins.scale(16),
        padding: Mixins.scale(16),
        backgroundColor: '#FCFBE1',
        borderColor: '#FAF9CB',
        marginTop: Mixins.scale(16)
    },
    textApprove: {
        color: '#D19305',
        marginLeft: Mixins.scale(10)
    }
});
export default React.memo(Menu);
