import React, { useEffect, useState } from 'react';
import { View, Image, StyleSheet, TouchableOpacity, Modal } from 'react-native';
import { MyText, Colors } from 'react-native-xwork';
import { Mixins } from '@mwg-sdk/styles';
import { IMAGES } from '@assets';
import { PermissionCamera, PermissionPhoto, ImageView } from '@components';

const AttachFile = ({
    listFileAttachment,
    isDetail,
    isReset
}: {
    listFileAttachment: (item: { base64: string }[]) => void;
    isDetail: boolean;
    isReset: boolean;
}) => {
    const initialImageState = {
        base64: ''
    };
    const [listImage, setListImage] = useState(
        Array(3).fill(initialImageState)
    );

    useEffect(() => {
        if (isReset) {
            setListImage(Array(3).fill(initialImageState));
        }
    }, [isReset]);

    useEffect(() => {
        if (listImage) {
            listFileAttachment(listImage);
        }
    }, [listImage]);

    const removeImage = (item: any) => {
        const index = listImage.indexOf(item);
        if (index !== -1) {
            const updatedImages = [...listImage];
            updatedImages[index].base64 = '';
            setListImage(updatedImages);
        }
    };

    const checkPermissions = (isOpenCamera = false, index: number) => {
        if (isOpenCamera) {
            PermissionCamera((response: any, error: any) => {
                console.log('attach camera response: ', response);
                if (response) {
                    const updatedImages = [...listImage];
                    if (Array.isArray(response)) {
                        response
                            .slice(0, 3)
                            .forEach((item: any, idx: number) => {
                                updatedImages[index + idx] = {
                                    base64: `data:@file/jpeg;base64,${item.base64}`
                                };
                            });
                    } else if (
                        typeof response === 'object' &&
                        response.base64
                    ) {
                        updatedImages[index] = {
                            base64: `data:@file/jpeg;base64,${response.base64}`
                        };
                    }
                    setListImage(updatedImages.slice(0, 3));
                }
                if (error) {
                    console.log('errorPer==', error.message);
                }
            });
        } else {
            PermissionPhoto((response: any, error: any) => {
                console.log('attach photo response: ', response);
                if (response) {
                    const updatedImages = [...listImage];
                    if (Array.isArray(response)) {
                        response
                            .slice(0, 3)
                            .forEach((item: any, idx: number) => {
                                updatedImages[index + idx] = {
                                    base64: `data:@file/jpeg;base64,${item.base64}`
                                };
                            });
                    } else if (
                        typeof response === 'object' &&
                        response.base64
                    ) {
                        updatedImages[index] = {
                            base64: `data:@file/jpeg;base64,${response.base64}`
                        };
                    }
                    setListImage(updatedImages.slice(0, 3));
                }
                if (error) {
                    console.log('errorPer==', error.message);
                }
            });
        }
    };
    console.log('get list image: ', listImage);
    return (
        <View
            style={{
                marginTop: Mixins.scale(20)
            }}>
            <MyText
                text="Thêm file đính kèm"
                category="bold.sub.title.2"
                style={{ color: Colors.NEUTRALS_100 }}
            />
            <MyText
                text="Tối đa 3 file"
                category="regular.caption.1"
                style={{ color: Colors.NEUTRALS_500 }}
            />
            <View
                style={{
                    marginTop: Mixins.scale(8),
                    flexDirection: 'row'
                }}>
                {listImage.map((item, index) => {
                    console.log('get');
                    return (
                        <View>
                            <TouchableOpacity
                                key={index}
                                onPress={() => checkPermissions(false, index)}
                                style={{
                                    height: Mixins.scale(72),
                                    width: Mixins.scale(72),
                                    backgroundColor: Colors.NEUTRALS_950,
                                    borderRadius: Mixins.scale(10),
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    marginRight: Mixins.scale(8),
                                    overflow: 'hidden'
                                }}>
                                {item.base64?.length > 0 ? (
                                    <View>
                                        <Image
                                            source={{
                                                uri: `${item.base64}`
                                            }}
                                            style={[Mixins.scaleImage(72, 72)]}
                                            resizeMode="cover"
                                        />
                                    </View>
                                ) : (
                                    <View
                                        style={{
                                            alignItems: 'center',
                                            justifyContent: 'center'
                                        }}>
                                        <Image
                                            source={IMAGES.ic_library}
                                            style={Mixins.scaleImage(24, 24)}
                                            resizeMode="contain"
                                        />
                                        <MyText
                                            text="Thêm ảnh"
                                            category="tag.item"
                                            style={{
                                                color: Colors.NEUTRALS_700,
                                                marginTop: Mixins.scale(4)
                                            }}
                                        />
                                    </View>
                                )}
                            </TouchableOpacity>
                            {item.base64?.length > 0 && (
                                <TouchableOpacity
                                    key={index}
                                    onPress={() => removeImage(item)}
                                    style={{
                                        zIndex: 1,
                                        position: 'absolute',
                                        left: Mixins.scale(60),
                                        bottom: Mixins.scale(62)
                                    }}>
                                    <Image
                                        source={IMAGES.ic_clear}
                                        style={[Mixins.scaleImage(16, 16)]}
                                        resizeMode="contain"
                                    />
                                </TouchableOpacity>
                            )}
                        </View>
                    );
                })}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({});

export default React.memo(AttachFile);
