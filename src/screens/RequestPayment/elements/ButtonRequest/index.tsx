import React from 'react';
import { View, StyleSheet } from 'react-native';
import { MyText, Colors, BaseButton } from 'react-native-xwork';
import { Mixins } from '@mwg-sdk/styles';
import { formattedPrice } from '@utils';

const ButtonRequest = ({
    onPressCreate,
    price,
    isDisable,
    isDetail,
    onPressCancel,
    onPressUpdate
}: {
    onPressCreate?: () => void;
    price: number;
    isDisable?: boolean;
    isDetail: boolean;
    onPressCancel?: () => void;
    onPressUpdate?: () => void;
}) => {
    return (
        <View style={styles.container}>
            <View style={styles.row}>
                <MyText
                    text="Tổng tiền thanh toán:"
                    category="regular.body.2"
                    style={styles.textRegular}
                />
                <MyText
                    text={formattedPrice(price)}
                    category="bold.body.1"
                    style={styles.textBold}
                />
            </View>

            {isDetail ? (
                <View style={styles.row}>
                    {onPressCancel && (
                        <BaseButton
                            onPress={onPressCancel}
                            text={'Hủy yêu cầu'}
                            textStyle={{
                                color: Colors.NEUTRALS_500
                            }}
                            buttonStyle={styles.btnCancel}
                        />
                    )}
                    {onPressUpdate && (
                        <BaseButton
                            onPress={onPressUpdate}
                            text={'Cập nhật'}
                            buttonStyle={styles.btnUpdate}
                        />
                    )}
                </View>
            ) : (
                <BaseButton
                    disabled={isDisable}
                    onPress={onPressCreate}
                    text={'Tạo yêu cầu'}
                    buttonStyle={styles.button}
                />
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        paddingTop: Mixins.scale(8),
        borderTopWidth: 1,
        borderTopColor: Colors.NEUTRALS_900,
        paddingHorizontal: Mixins.scale(16),
        paddingVertical: Mixins.scale(16)
    },
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    textRegular: {
        color: Colors.NEUTRALS_500
    },
    textBold: {
        color: Colors.RED_500
    },
    button: {
        marginTop: Mixins.scale(8),
        backgroundColor: Colors.BADGE_GHOST_BLUE_TEXT,
        height: Mixins.scale(56)
    },
    btnCancel: {
        marginTop: Mixins.scale(8),
        backgroundColor: Colors.WHITE,
        height: Mixins.scale(56),
        borderWidth: 1,
        borderColor: Colors.NEUTRALS_900,
        flex: 1
    },
    btnUpdate: {
        marginTop: Mixins.scale(8),
        backgroundColor: Colors.BADGE_GHOST_BLUE_TEXT,
        height: Mixins.scale(56),
        marginLeft: Mixins.scale(8),
        flex: 1
    }
});

export default React.memo(ButtonRequest);
