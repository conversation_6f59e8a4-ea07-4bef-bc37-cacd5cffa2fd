import React, {
    useState,
    useEffect,
    useMemo,
    useCallback,
    useRef
} from 'react';
import {
    TouchableOpacity,
    View,
    Image,
    StyleSheet,
    LayoutAnimation,
    Platform,
    UIManager,
    TextInput,
    FlatList,
    Keyboard
} from 'react-native';
import { MyText, Colors, BaseBottomSheet } from 'react-native-xwork';
import { Mixins } from '@mwg-sdk/styles';
import { IMAGES } from '@assets';
import { TransactionDetailEntity, SearchExpenseContent } from '@types';
import { InputField } from '@components';
import { convertUTCDateToLocalDate, formatDate } from '@utils';
import * as actionPaymentApproval from '../../../../store/action';
import { useDispatch, useSelector } from 'react-redux';
import { RootReducerType } from '../../../../store/rootReducers';

const InfoContentRequest = ({
    onContentRequest,
    selectedItem,
    isDetail,
    transactionDetailEntities,
    isReset,
    setIsHideListCustomer,
    isHideListCustomer,
    emptyFields
}: {
    onContentRequest: (data: {
        transactionDetailEntities: TransactionDetailEntity[];
        totalAmount: number;
    }) => void;
    selectedItem?: SearchExpenseContent;
    isDetail: boolean;
    transactionDetailEntities?: TransactionDetailEntity[];
    isReset: boolean;
    setIsHideListCustomer: React.Dispatch<React.SetStateAction<boolean>>;
    isHideListCustomer: boolean;
    emptyFields: Array<{
        index: number;
        emptyFields: string[];
    }> | null;
}) => {
    const dispatch = useDispatch();
    const searchCustomerState = useSelector(
        (state: RootReducerType) => state.paymentApprovalReducer.searchCustomer
    );
    const { ModalCalendarNew } = useSelector(
        (state: RootReducerType) => state.commonReducer.xworkData
    );

    const [isShowMore, setShowMore] = useState(false);
    const [isShowTaxRate, setIsShowTaxRate] = useState(false);
    const [indexVAT, setIndexVAT] = useState(-1);
    const initData = {
        customerID: 5,
        customerName: '',
        customerAddress: '',
        customerTax: '',
        invoiceID: '',
        invoiceSymbol: '',
        denominator: '',
        invoiceDate: new Date().getTime(),
        totalAmount: null,
        VAT: 0,
        content: ''
    };
    const [listInfoContent, setListInfoContent] = useState<
        TransactionDetailEntity[]
    >([]);
    const cusNameRef = useRef<TextInput | null>(null);
    const cusAddessRef = useRef<TextInput | null>(null);
    const cusTaxRef = useRef<TextInput | null>(null);
    const invoiceIdRef = useRef<TextInput | null>(null);
    const invoiceSymbolRef = useRef<TextInput | null>(null);
    const denominatorRef = useRef<TextInput | null>(null);
    const vatRef = useRef<TextInput | null>(null);
    const contentRef = useRef<TextInput | null>(null);
    const [validateMsg, setValidateMsg] = useState<string>('');
    const [indexSearch, setIndexSearch] = useState<number>(-1);
    const [indexCalendar, setIndexCalendar] = useState<number>(-1);

    useEffect(() => {
        if (isReset) {
            setListInfoContent([initData]);
        }
    }, [isReset]);

    useEffect(() => {
        if (isDetail && transactionDetailEntities) {
            setListInfoContent(transactionDetailEntities);
        } else {
            setListInfoContent([initData]);
        }
    }, [transactionDetailEntities]);

    useEffect(() => {
        if (
            Platform.OS === 'android' &&
            UIManager.setLayoutAnimationEnabledExperimental
        ) {
            UIManager.setLayoutAnimationEnabledExperimental(true);
        }
    }, []);

    useEffect(() => {
        recalculateTotalAmount(listInfoContent);
    }, [listInfoContent]);

    const toggleExpand = () => {
        const customLayoutAnimation = {
            duration: 300,
            update: { type: LayoutAnimation.Types.easeInEaseOut }
        };
        LayoutAnimation.configureNext(customLayoutAnimation);
        setShowMore(!isShowMore);
    };

    const removeItemByIndex = (index: number) => {
        setListInfoContent((prev) => prev.filter((_, i) => i !== index));
    };

    const recalculateTotalAmount = useCallback(
        (list: TransactionDetailEntity[]) => {
            const total = list.reduce(
                (sum, item) => sum + (Number(item.totalAmount) || 0),
                0
            );
            onContentRequest({
                transactionDetailEntities: list,
                totalAmount: total
            });
        },
        [onContentRequest]
    );

    const updateItemField = useCallback(
        (index: number, field: keyof TransactionDetailEntity, value: any) => {
            setListInfoContent((prev) => {
                const updatedList = prev.map((item, i) => {
                    if (i === index) {
                        if (field === 'customerName') {
                            if (value.length > 0) {
                                setTimeout(() => {
                                    searchCustomerInfo(value);
                                }, 200);
                            }
                        }
                        return { ...item, [field]: value };
                    }
                    return item;
                });
                recalculateTotalAmount(updatedList);
                return updatedList;
            });
        },
        []
    );

    const searchCustomerInfo = (key: string) => {
        setIsHideListCustomer(false);
        dispatch(actionPaymentApproval.searchCustomer(key) as any);
    };

    const focusNextInput = (isValidate: boolean, ref?: any) => {
        if (isValidate && ref) {
            ref?.current?.focus();
        }
    };

    const ListCustomerInfo = useCallback(
        ({ index }: { index: number }) => {
            if (
                searchCustomerState?.data &&
                indexSearch === index &&
                !isHideListCustomer
            ) {
                return (
                    <FlatList
                        style={styles.flatlistContainer}
                        data={searchCustomerState.data}
                        showsVerticalScrollIndicator={false}
                        keyExtractor={(item, index) => index.toString()}
                        renderItem={({ item, idx }) => {
                            return (
                                <TouchableOpacity
                                    key={idx}
                                    onPress={() => {
                                        updateItemField(
                                            index,
                                            'customerID',
                                            item.customerid
                                        );
                                        updateItemField(
                                            index,
                                            'customerName',
                                            `${item.customerid} - ${item.customername}`
                                        );
                                        updateItemField(
                                            index,
                                            'customerAddress',
                                            item.customeraddress
                                        );
                                        updateItemField(
                                            index,
                                            'customerTax',
                                            item.taxid
                                        );
                                        setIndexSearch(-1);
                                    }}
                                    style={{
                                        paddingHorizontal: Mixins.scale(16)
                                    }}>
                                    <MyText
                                        text={`${item.customerid} - ${item.customername}`}
                                        category="regular.body"
                                        style={{
                                            color: Colors.NEUTRALS_100
                                        }}
                                    />
                                    <View
                                        style={[
                                            styles.lineSeparator,
                                            {
                                                marginVertical: Mixins.scale(16)
                                            }
                                        ]}
                                    />
                                </TouchableOpacity>
                            );
                        }}
                    />
                );
            }
            return null;
        },
        [searchCustomerState, indexSearch, isHideListCustomer]
    );

    const getErrorMessage = (
        field: string,
        index: number,
        emptyFields: { index: number; emptyFields: string[] }[]
    ) => {
        const matchedItem = emptyFields?.find((item) => item.index === index);
        if (matchedItem?.emptyFields?.includes(field)) {
            return `Vui lòng nhập ${getMissingFieldLabel(field)}`;
        }
        return '';
    };

    const getMissingFieldLabel = (field: string) => {
        switch (field) {
            case 'totalAmount':
                return 'số tiền thanh toán lớn hơn 0 và nhỏ hơn 1 tỷ.';
            case 'invoiceID':
                return 'số hoá đơn';
            case 'invoiceSymbol':
                return 'số ký hiệu HĐ';
            case 'denominator':
                return 'mẫu số hoá đơn';
            default:
                return '';
        }
    };

    const FeeInfoItem = useMemo(() => {
        console.log('list info content: ', listInfoContent);
        return (
            <View>
                {listInfoContent.map((item, index) => {
                    return (
                        <View key={index} style={styles.expandDataWrapper}>
                            <InputField
                                placeholder="Tên khách hàng"
                                value={item.customerName}
                                onChange={(text) => {
                                    updateItemField(
                                        index,
                                        'customerName',
                                        text
                                    );
                                    setIndexSearch(index);
                                }}
                                clearValue={() => {
                                    updateItemField(index, 'customerName', '');
                                    setIsHideListCustomer(true);
                                }}
                                ref={cusNameRef}
                                returnKeyType="next"
                                onSubmitEditing={(isValidate) =>
                                    focusNextInput(isValidate, cusAddessRef)
                                }
                            />

                            <ListCustomerInfo index={index} />

                            <InputField
                                placeholder="Địa chỉ"
                                value={item.customerAddress}
                                onChange={(text) =>
                                    updateItemField(
                                        index,
                                        'customerAddress',
                                        text
                                    )
                                }
                                clearValue={() =>
                                    updateItemField(
                                        index,
                                        'customerAddress',
                                        ''
                                    )
                                }
                                ref={cusAddessRef}
                                returnKeyType="next"
                                onSubmitEditing={(isValidate) =>
                                    focusNextInput(isValidate, cusTaxRef)
                                }
                            />
                            <InputField
                                placeholder="Mã số thuế"
                                value={item.customerTax}
                                onChange={(text) =>
                                    updateItemField(index, 'customerTax', text)
                                }
                                clearValue={() =>
                                    updateItemField(index, 'customerTax', '')
                                }
                                returnKeyType="next"
                                ref={cusTaxRef}
                                onSubmitEditing={(isValidate) => {
                                    if (
                                        Boolean(
                                            selectedItem?.isRequiredInvoice
                                        ) ||
                                        Boolean(selectedItem?.isHavingInvoice)
                                    ) {
                                        focusNextInput(
                                            isValidate,
                                            invoiceIdRef
                                        );
                                    } else {
                                        focusNextInput(isValidate, vatRef);
                                    }
                                }}
                            />
                            {(selectedItem?.isRequiredInvoice === 1 ||
                                selectedItem?.isHavingInvoice === 1) && (
                                <View>
                                    <InputField
                                        placeholder="Nhập số hoá đơn"
                                        value={item.invoiceID}
                                        onChange={(text) =>
                                            updateItemField(
                                                index,
                                                'invoiceID',
                                                text
                                            )
                                        }
                                        clearValue={() =>
                                            updateItemField(
                                                index,
                                                'invoiceID',
                                                ''
                                            )
                                        }
                                        onSubmitEditing={(isValidate) =>
                                            focusNextInput(
                                                isValidate,
                                                invoiceSymbolRef
                                            )
                                        }
                                        ref={invoiceIdRef}
                                        returnKeyType="next"
                                        isRequired={Boolean(
                                            selectedItem?.isRequiredInvoice
                                        )}
                                        msgError={getErrorMessage(
                                            'invoiceID',
                                            index,
                                            emptyFields
                                        )}
                                    />
                                    <InputField
                                        placeholder="Nhập ký hiệu HĐ"
                                        value={item.invoiceSymbol}
                                        onChange={(text) =>
                                            updateItemField(
                                                index,
                                                'invoiceSymbol',
                                                text
                                            )
                                        }
                                        clearValue={() =>
                                            updateItemField(
                                                index,
                                                'invoiceSymbol',
                                                ''
                                            )
                                        }
                                        onSubmitEditing={(isValidate) =>
                                            focusNextInput(
                                                isValidate,
                                                denominatorRef
                                            )
                                        }
                                        ref={invoiceSymbolRef}
                                        returnKeyType="next"
                                        isRequired={Boolean(
                                            selectedItem?.isRequiredInvoice
                                        )}
                                        msgError={getErrorMessage(
                                            'invoiceSymbol',
                                            index,
                                            emptyFields
                                        )}
                                    />
                                    <InputField
                                        placeholder="Nhập mẫu số hoá đơn"
                                        value={item.denominator}
                                        onChange={(text) =>
                                            updateItemField(
                                                index,
                                                'denominator',
                                                text
                                            )
                                        }
                                        clearValue={() =>
                                            updateItemField(
                                                index,
                                                'denominator',
                                                ''
                                            )
                                        }
                                        ref={denominatorRef}
                                        returnKeyType="next"
                                        isRequired={Boolean(
                                            selectedItem?.isRequiredInvoice
                                        )}
                                        msgError={getErrorMessage(
                                            'denominator',
                                            index,
                                            emptyFields
                                        )}
                                    />
                                </View>
                            )}

                            <TouchableOpacity
                                onPress={() => {
                                    setIndexCalendar(index);
                                }}
                                style={styles.dateTime}>
                                <MyText
                                    text={formatDate(item.invoiceDate)}
                                    category="regular.body"
                                    style={{ color: Colors.NEUTRALS_100 }}
                                />
                                <Image
                                    source={IMAGES.ic_calendar}
                                    style={Mixins.scaleImage(20, 20)}
                                    resizeMode="contain"
                                />
                            </TouchableOpacity>
                            {index === indexCalendar && (
                                <ModalCalendarNew
                                    isVisible={index === indexCalendar}
                                    startDate={convertUTCDateToLocalDate(
                                        new Date(item.invoiceDate)
                                    )}
                                    onChangeSelectedDate={(date: string) => {
                                        const formatDate = new Date(
                                            date
                                        ).getTime() as any;
                                        updateItemField(
                                            index,
                                            'invoiceDate',
                                            formatDate
                                        );
                                        setIndexCalendar(-1);
                                    }}
                                    onPressDimiss={() => setIndexCalendar(-1)}
                                    singleChoose
                                />
                            )}

                            <InputField
                                placeholder="Số tiền VAT"
                                value={
                                    item.totalAmount > 0
                                        ? item.totalAmount.toString()
                                        : ''
                                }
                                onChange={(text) => {
                                    const amount = Number(text);
                                    let message = '';

                                    if (amount <= 0 || amount >= 1000000000) {
                                        message =
                                            'Vui lòng nhập số tiền thanh toán lớn hơn 0 và nhỏ hơn 1 tỷ.';
                                    } else {
                                        message = '';
                                    }

                                    setValidateMsg(message);
                                    updateItemField(
                                        index,
                                        'totalAmount',
                                        amount
                                    );
                                }}
                                clearValue={() =>
                                    updateItemField(index, 'totalAmount', 0)
                                }
                                keyboardType="numeric"
                                returnKeyType="next"
                                isRequired
                                ref={vatRef}
                                msgError={
                                    validateMsg
                                        ? validateMsg
                                        : getErrorMessage(
                                              'totalAmount',
                                              index,
                                              emptyFields
                                          )
                                }
                                isCurrency
                            />
                            <TouchableOpacity
                                onPress={() => {
                                    setIndexVAT(index);
                                    setIsShowTaxRate(true);
                                }}
                                style={styles.taxRateWrapper}>
                                <MyText
                                    text={
                                        item.VAT !== null
                                            ? `${item.VAT}`
                                            : 'Thuế suất'
                                    }
                                    category="regular.body"
                                    style={{
                                        color:
                                            item.VAT !== null
                                                ? Colors.NEUTRALS_100
                                                : Colors.NEUTRALS_500
                                    }}
                                />
                                <Image
                                    source={IMAGES.ic_arrow_down}
                                    style={Mixins.scaleImage(20, 20)}
                                    resizeMode="contain"
                                />
                            </TouchableOpacity>

                            <InputField
                                placeholder="Ghi chú"
                                value={item.content}
                                onChange={(text) =>
                                    updateItemField(index, 'content', text)
                                }
                                clearValue={() =>
                                    updateItemField(index, 'content', '')
                                }
                                height={80}
                                multiline
                                ref={contentRef}
                                onSubmitEditing={() => {
                                    Keyboard.dismiss();
                                }}
                            />

                            <TouchableOpacity
                                style={styles.btnDelete}
                                onPress={() => removeItemByIndex(index)}>
                                <MyText
                                    text="Xoá"
                                    category="button.small"
                                    style={{ color: Colors.BRAND_600 }}
                                />
                            </TouchableOpacity>
                        </View>
                    );
                })}
            </View>
        );
    }, [
        listInfoContent,
        selectedItem,
        validateMsg,
        isReset,
        searchCustomerState,
        isHideListCustomer,
        emptyFields,
        indexCalendar
    ]);

    const BottomSheetTaxRate = useMemo(() => {
        const data = [
            { taxRate: '0' },
            { taxRate: '5' },
            { taxRate: '8' },
            { taxRate: '10' },
            { taxRate: '11' },
            { taxRate: '12' }
        ];
        return (
            <BaseBottomSheet
                isModalVisible={isShowTaxRate}
                toggleModal={() => {
                    setIsShowTaxRate(false);
                }}
                titleName="Chọn thuế suất">
                {data.map((item, index) => (
                    <TouchableOpacity
                        onPress={() => {
                            updateItemField(indexVAT, 'VAT', item.taxRate);
                            setIsShowTaxRate(false);
                        }}
                        key={index}
                        style={{
                            paddingHorizontal: Mixins.scale(16)
                        }}>
                        <MyText
                            text={item.taxRate}
                            category="regular.body"
                            style={{ color: Colors.NEUTRALS_100 }}
                        />
                        <View
                            style={[
                                styles.lineSeparator,
                                {
                                    marginVertical: Mixins.scale(16)
                                }
                            ]}
                        />
                    </TouchableOpacity>
                ))}
            </BaseBottomSheet>
        );
    }, [isShowTaxRate, indexVAT]);

    return (
        <View
            style={{
                marginTop: Mixins.scale(24)
            }}>
            <TouchableOpacity
                onPress={toggleExpand}
                activeOpacity={0.8}
                style={styles.common}>
                <View style={styles.common}>
                    <Image
                        source={IMAGES.ic_plus_circle}
                        style={[
                            Mixins.scaleImage(24, 24),
                            {
                                marginRight: Mixins.scale(12)
                            }
                        ]}
                        resizeMode="contain"
                    />
                    <MyText
                        text="Thông tin cho nội dung chi phí"
                        category="bold.body.1"
                        style={{ color: Colors.NEUTRALS_100 }}
                    />
                </View>
                <Image
                    source={IMAGES.ic_arrow_down}
                    style={[
                        Mixins.scaleImage(20, 20),
                        {
                            marginRight: Mixins.scale(12),
                            transform: [
                                { rotate: isShowMore ? '180deg' : '0deg' }
                            ]
                        }
                    ]}
                    resizeMode="contain"
                />
            </TouchableOpacity>
            {isShowMore && (
                <View>
                    {FeeInfoItem}

                    <TouchableOpacity
                        onPress={() => {
                            setListInfoContent((prev) => [...prev, initData]);
                        }}
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            marginTop: Mixins.scale(20)
                        }}>
                        <Image
                            source={IMAGES.ic_plus}
                            style={Mixins.scaleImage(20, 20)}
                            resizeMode="contain"
                        />
                        <MyText
                            text="Thêm nội dung chi phí"
                            category="button.small"
                            style={{
                                color: Colors.NEUTRALS_500,
                                marginLeft: Mixins.scale(4)
                            }}
                        />
                    </TouchableOpacity>
                </View>
            )}

            <View
                style={[
                    styles.lineSeparator,
                    {
                        marginTop: Mixins.scale(20)
                    }
                ]}
            />
            {BottomSheetTaxRate}
        </View>
    );
};

const styles = StyleSheet.create({
    common: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    taxRateWrapper: {
        height: Mixins.scale(45),
        borderWidth: 1,
        borderColor: Colors.NEUTRALS_900,
        paddingHorizontal: Mixins.scale(16),
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: Colors.WHITE,
        borderRadius: Mixins.scale(16),
        marginBottom: Mixins.scale(10)
    },
    noteWrapper: {
        height: Mixins.scale(80),
        borderWidth: 1,
        borderColor: Colors.NEUTRALS_900,
        paddingHorizontal: Mixins.scale(16),
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: Colors.WHITE,
        borderRadius: Mixins.scale(16)
    },
    expandDataWrapper: {
        backgroundColor: Colors.NEUTRALS_950,
        borderRadius: Mixins.scale(16),
        paddingHorizontal: Mixins.scale(10),
        paddingVertical: Mixins.scale(16),
        marginTop: Mixins.scale(20)
    },
    lineSeparator: {
        height: 1,
        backgroundColor: Colors.NEUTRALS_900
    },
    inputWrapper: {
        height: Mixins.scale(45),
        borderWidth: 1,
        borderColor: Colors.NEUTRALS_900,
        paddingHorizontal: Mixins.scale(16),
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: Colors.WHITE,
        borderRadius: Mixins.scale(16),
        marginBottom: Mixins.scale(10)
    },
    textInput: {
        flex: 1,
        paddingHorizontal: Mixins.scale(8),
        color: Colors.NEUTRALS_100
    },
    btnDelete: {
        alignSelf: 'flex-end',
        marginTop: Mixins.scale(12)
    },
    dateTime: {
        borderWidth: 1,
        paddingHorizontal: Mixins.scale(16),
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: Colors.WHITE,
        borderRadius: Mixins.scale(16),
        marginBottom: Mixins.scale(10),
        height: Mixins.scale(56),
        borderColor: Colors.NEUTRALS_900
    },
    flatlistContainer: {
        position: 'absolute',
        zIndex: 1,
        backgroundColor: Colors.WHITE,
        maxHeight: Mixins.scale(180),
        marginBottom: Mixins.scale(4),
        left: Mixins.scale(10),
        top: Mixins.scale(62),
        right: Mixins.scale(10),
        borderRadius: Mixins.scale(16),
        borderWidth: 1,
        borderColor: Colors.NEUTRALS_950,
        paddingTop: Mixins.scale(16)
    }
});

export default React.memo(InfoContentRequest);
