import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
    TouchableOpacity,
    View,
    Image,
    StyleSheet,
    TextInput,
    FlatList
} from 'react-native';
import { MyText, Colors, BaseBottomSheet } from 'react-native-xwork';
import { Mixins } from '@mwg-sdk/styles';
import { IMAGES } from '@assets';
import { useDispatch, useSelector } from 'react-redux';
import * as actionPaymentApproval from '../../../../store/action';
import { RootReducerType } from '../../../../store/rootReducers';
import { SearchExpenseContent } from '@types';
import { useDebounce } from '@hooks';

const SelectContentPayment = ({
    onSelected,
    isDetail,
    setShowAlert,
    setReset,
    isReset
}: {
    onSelected: (item: SearchExpenseContent) => void;
    isDetail: boolean;
    setShowAlert?: React.Dispatch<any>;
    setReset: React.Dispatch<any>;
    isReset: boolean;
}) => {
    const dispatch = useDispatch();
    const searchExpenseContentState = useSelector(
        (state: RootReducerType) =>
            state.paymentApprovalReducer.searchExpenseContent
    );
    const createTransactionState = useSelector(
        (state: RootReducerType) =>
            state.paymentApprovalReducer.createTransaction
    );
    const [isVisible, setVisible] = useState(false);
    const [searchText, setSearchText] = useState('');
    const debouncedSearchTerm = useDebounce(searchText, 300);

    const [selectedItem, setSelectedItem] = useState<
        SearchExpenseContent | any
    >(null);
    const [storeItem, setStoreItem] = useState<SearchExpenseContent | any>(
        null
    );

    useEffect(() => {
        if (debouncedSearchTerm) {
            fetchData(debouncedSearchTerm);
        }
    }, [debouncedSearchTerm]);

    useEffect(() => {
        if (selectedItem) {
            onSelected(selectedItem);
        }
    }, [selectedItem]);

    useEffect(() => {
        if (createTransactionState.isSuccess) {
            setSelectedItem(null);
        }
    }, [createTransactionState]);

    const fetchData = (search?: string) => {
        dispatch(actionPaymentApproval.searchExpenseContent(search) as any);
    };

    useEffect(() => {
        if (isReset && storeItem) {
            setSelectedItem(storeItem);
        }
    }, [isReset, storeItem]);

    const renderItem = ({
        item,
        index
    }: {
        item: SearchExpenseContent;
        index: number;
    }) => {
        return (
            <TouchableOpacity
                key={index}
                activeOpacity={0.8}
                onPress={() => {
                    setVisible(false);
                    if (setShowAlert) {
                        setShowAlert(true);
                    }
                    setStoreItem(item);
                }}
                style={{
                    marginTop: Mixins.scale(16)
                }}>
                <MyText
                    text={item.expenseContentName}
                    category="regular.body"
                    style={{
                        color: Colors.NEUTRALS_100
                    }}
                />
            </TouchableOpacity>
        );
    };

    const ItemSeparatorComponent = () => {
        return (
            <View
                style={{
                    height: 1,
                    backgroundColor: Colors.NEUTRALS_900,
                    marginTop: Mixins.scale(16)
                }}
            />
        );
    };

    const ListEmptyComponent = useCallback(() => {
        if (searchExpenseContentState.isError) {
            return (
                <View
                    style={{
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}>
                    <MyText text="Đã có lỗi xảy ra. Vui lòng thử lại sau." />
                </View>
            );
        }
        return null;
    }, [searchExpenseContentState]);

    const RenderBottomSheet = useMemo(() => {
        return (
            <BaseBottomSheet
                isModalVisible={isVisible}
                toggleModal={() => {
                    setVisible(false);
                    setSearchText('');
                }}
                titleName="Chọn nội dung chi phí"
                isHeight={0.7}>
                <View style={styles.bottomSheetContainer}>
                    <View style={styles.searchContainer}>
                        <View style={styles.searchInputContainer}>
                            <Image
                                source={IMAGES.ic_search}
                                style={Mixins.scaleImage(20, 20)}
                                resizeMode="contain"
                            />
                            <TextInput
                                placeholder="Tìm kiếm nội dung chi phí..."
                                placeholderTextColor={Colors.NEUTRALS_500}
                                value={searchText}
                                onChangeText={setSearchText}
                                style={styles.textInput}
                            />
                        </View>

                        {searchText?.trim()?.length > 0 && (
                            <TouchableOpacity
                                activeOpacity={0.8}
                                onPress={() => setSearchText('')}
                                style={styles.clearButton}>
                                <Image
                                    source={IMAGES.ic_clear}
                                    style={Mixins.scaleImage(20, 20)}
                                    resizeMode="contain"
                                />
                            </TouchableOpacity>
                        )}
                    </View>
                    <FlatList
                        style={{
                            marginTop: Mixins.scale(4),
                            marginBottom: Mixins.scale(24)
                        }}
                        data={searchExpenseContentState.data}
                        renderItem={renderItem}
                        keyExtractor={(item, index) => index.toString()}
                        showsVerticalScrollIndicator={false}
                        ItemSeparatorComponent={ItemSeparatorComponent}
                        ListEmptyComponent={ListEmptyComponent}
                    />
                </View>
            </BaseBottomSheet>
        );
    }, [isVisible, searchText, searchExpenseContentState]);

    return (
        <View>
            <MyText
                text="Chọn nội dung chi phí "
                category="regular.sub.title.2"
                style={{ color: Colors.NEUTRALS_100 }}>
                <MyText
                    text="*"
                    category="regular.sub.title.2"
                    style={{ color: Colors.RED_500 }}
                />
            </MyText>
            <TouchableOpacity
                activeOpacity={0.8}
                style={styles.selectButton}
                onPress={() => {
                    fetchData();
                    setVisible(true);
                    setReset(false);
                }}>
                <View style={{ flex: 8 }}>
                    <MyText
                        text={
                            selectedItem?.expenseContentName?.length > 0
                                ? selectedItem?.expenseContentName
                                : 'Chọn nội dung chi phí'
                        }
                        category="regular.body"
                        style={{
                            color:
                                selectedItem?.expenseContentName?.length > 0
                                    ? Colors.NEUTRALS_100
                                    : Colors.NEUTRALS_500
                        }}
                        numberOfLines={1}
                        ellipsizeMode="tail"
                    />
                </View>

                <View style={{ flex: 0.5 }}>
                    <Image
                        source={IMAGES.ic_arrow_down}
                        style={[Mixins.scaleImage(20, 20)]}
                        resizeMode="contain"
                    />
                </View>
            </TouchableOpacity>
            {RenderBottomSheet}
        </View>
    );
};

const styles = StyleSheet.create({
    bottomSheetContainer: {
        flex: 1
    },
    searchContainer: {
        backgroundColor: Colors.NEUTRALS_950,
        borderWidth: 1,
        borderColor: Colors.NEUTRALS_900,
        borderRadius: Mixins.scale(16),
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: Mixins.scale(16),
        height: Mixins.scale(56)
    },
    searchInputContainer: {
        flex: 8,
        flexDirection: 'row',
        alignItems: 'center'
    },
    textInput: {
        backgroundColor: Colors.NEUTRALS_950,
        flex: 1,
        paddingHorizontal: Mixins.scale(8)
    },
    clearButton: {
        flex: 0.5
    },
    selectButton: {
        marginTop: Mixins.scale(6),
        borderWidth: 1,
        borderColor: Colors.NEUTRALS_900,
        borderRadius: Mixins.scale(16),
        paddingHorizontal: Mixins.scale(16),
        flexDirection: 'row',
        alignItems: 'center',
        height: Mixins.scale(56),
        justifyContent: 'space-between',
        flex: 1
    }
});

export default React.memo(SelectContentPayment);
