import React, { useCallback, useEffect, useState } from 'react';
import {
    TouchableOpacity,
    View,
    Image,
    StyleSheet,
    Platform,
    TouchableWithoutFeedback
} from 'react-native';
import { MyText, Colors, NotifyModal } from 'react-native-xwork';
import { WrapperContainerBase } from '@mwg-kits/components';
import { Mixins } from '@mwg-sdk/styles';
import {
    SelectContentPayment,
    ButtonRequest,
    InfoContentPayment,
    AttachFile
} from './elements';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import * as actionPaymentApproval from '../../store/action';
import { useDispatch, useSelector } from 'react-redux';
import { SearchExpenseContent, TransactionDetailEntity } from '@types';
import { RootReducerType } from '../../store/rootReducers';

const RequestPayment = (props: any) => {
    const { navigation } = props;
    const dispatch = useDispatch();
    const createTransactionState = useSelector(
        (state: RootReducerType) =>
            state.paymentApprovalReducer.createTransaction
    );
    const { userInfo } = useSelector(
        (state: RootReducerType) => state.commonReducer.xworkData
    );
    const [selectedItem, setSelectedItem] = useState<
        SearchExpenseContent | any
    >(null);
    const [fileAttachment, setFileAttachment] = useState<string[]>(['']);
    const [transactionObject, setTransactionObject] = useState<{
        transactionDetailEntities: TransactionDetailEntity[];
        totalAmount: number;
    }>({ transactionDetailEntities: [], totalAmount: 0 });
    const [isShowAlert, setShowAlert] = useState(false);
    const [isReset, setReset] = useState(false);
    const [isHideListCustomer, setIsHideListCustomer] = useState(false);
    const [emptyFields, setEmptyFields] = useState<Array<{
        index: number;
        emptyFields: string[];
    }> | null>(null);

    useEffect(() => {
        if (createTransactionState.isSuccess) {
            setSelectedItem(null);
        }
    }, [createTransactionState]);

    useEffect(() => {
        if (selectedItem) {
            setEmptyFields(null);
        }
    }, [selectedItem]);

    const componentsLeft = () => {
        return (
            <View style={styles.componentLeft}>
                <TouchableOpacity
                    hitSlop={styles.btnArrow}
                    onPress={() => navigation.goBack()}>
                    <Image
                        resizeMode="contain"
                        source={{
                            uri: 'ic_direct'
                        }}
                        style={[Mixins.scaleImage(32, 32), styles.iconLeft]}
                    />
                </TouchableOpacity>
                <MyText
                    text={'Tạo yêu cầu thanh toán'}
                    addSize={4}
                    typeFont="bold"
                    numberOfLines={1}
                    style={styles.headTitle}
                />
            </View>
        );
    };

    const Description = useCallback(() => {
        if (selectedItem) {
            return (
                <View style={{ marginTop: Mixins.scale(20) }}>
                    <MyText
                        text="Mô tả:"
                        category="regular.body.2"
                        style={{
                            color: Colors.NEUTRALS_500,
                            textDecorationLine: 'underline'
                        }}
                    />
                    <MyText
                        text={selectedItem.description}
                        category="regular.body.2"
                        style={styles.description}
                    />
                </View>
            );
        }
        return null;
    }, [selectedItem]);

    const StorePayment = useCallback(() => {
        return (
            <View style={styles.storeWrapper}>
                <MyText text="Thanh toán chi phí cho siêu thị" />
                <MyText
                    text={`${userInfo?.storeId} - ${userInfo?.storeName}`}
                    category="bold.body.1"
                    style={styles.storeName}
                    numberOfLines={1}
                />
            </View>
        );
    }, [userInfo]);

    const actionRequestPayment = useCallback(() => {
        const isValidatedContent = checkEnableBtnRequest();
        const emptyField = getInvalidEntitiesWithEmptyFields();
        const body = {
            transactionObject: {
                transactionDetailEntities:
                    transactionObject.transactionDetailEntities,
                base64Attachments:
                    fileAttachment?.length > 0 ? fileAttachment : [],
                totalAmount: transactionObject.totalAmount
            },
            expenseContentId: selectedItem?.expenseContentId,
            expenseContentName: selectedItem?.expenseContentName
        };
        console.log('body request payment: ', body);
        if (emptyField) {
            setEmptyFields(emptyField);
        }
        if (isValidatedContent) {
            dispatch(actionPaymentApproval.createTransaction(body) as any);
        }
    }, [selectedItem, transactionObject, fileAttachment]);

    const checkEnableBtnRequest = () => {
        const validateForm = transactionObject.transactionDetailEntities?.every(
            (entity) => {
                const { totalAmount, invoiceID, invoiceSymbol, denominator } =
                    entity;
                const isRequired = Boolean(selectedItem?.isRequiredInvoice);

                return (
                    totalAmount > 0 &&
                    totalAmount < 1000000000 &&
                    (!isRequired ||
                        (invoiceID?.length > 0 &&
                            invoiceSymbol?.length > 0 &&
                            denominator?.length > 0))
                );
            }
        );

        return selectedItem && transactionObject.totalAmount && validateForm
            ? true
            : false;
    };

    const getInvalidEntitiesWithEmptyFields = () => {
        return transactionObject.transactionDetailEntities
            ?.map((entity, index) => {
                const { totalAmount, invoiceID, invoiceSymbol, denominator } =
                    entity;
                const isRequired = Boolean(selectedItem?.isRequiredInvoice);

                let emptyFields: string[] = [];

                if (!(totalAmount > 0 && totalAmount < 1000000000)) {
                    emptyFields.push('totalAmount');
                }
                if (isRequired) {
                    if (!invoiceID?.length) emptyFields.push('invoiceID');
                    if (!invoiceSymbol?.length)
                        emptyFields.push('invoiceSymbol');
                    if (!denominator?.length) emptyFields.push('denominator');
                }

                return emptyFields.length > 0 ? { index, emptyFields } : null;
            })
            .filter(Boolean);
    };

    console.log('transaction object: ', transactionObject);
    console.log('is reset: ', isReset);
    return (
        <View style={styles.container}>
            <WrapperContainerBase componentsLeft={componentsLeft}>
                <StorePayment />
                <KeyboardAwareScrollView
                    behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                    contentContainerStyle={styles.scrollView}
                    showsVerticalScrollIndicator={false}
                    keyboardShouldPersistTaps="handled">
                    <TouchableWithoutFeedback
                        onPress={() => setIsHideListCustomer(true)}>
                        <View>
                            <SelectContentPayment
                                onSelected={setSelectedItem}
                                isDetail={false}
                                setShowAlert={setShowAlert}
                                setReset={setReset}
                                isReset={isReset}
                            />
                            {selectedItem && (
                                <View>
                                    <Description />
                                    <InfoContentPayment
                                        onContentRequest={setTransactionObject}
                                        selectedItem={selectedItem}
                                        isDetail={false}
                                        isReset={isReset}
                                        setIsHideListCustomer={
                                            setIsHideListCustomer
                                        }
                                        isHideListCustomer={isHideListCustomer}
                                        emptyFields={emptyFields}
                                    />

                                    <AttachFile
                                        listFileAttachment={(item) => {
                                            const newItem = item
                                                .map((obj) => obj.base64)
                                                .filter(
                                                    (base64) =>
                                                        base64 &&
                                                        base64?.length > 0
                                                );
                                            setFileAttachment(newItem);
                                        }}
                                        isDetail={false}
                                        isReset={isReset}
                                    />
                                </View>
                            )}
                        </View>
                    </TouchableWithoutFeedback>
                </KeyboardAwareScrollView>
                {selectedItem && (
                    <ButtonRequest
                        onPressCreate={actionRequestPayment}
                        price={transactionObject.totalAmount}
                        isDetail={false}
                    />
                )}
                <NotifyModal
                    isVisible={isShowAlert}
                    title={'Thông báo'}
                    typeNotify="option"
                    typeIcon="info"
                    content={
                        'Thay đổi nội dung chi phí sẽ xóa hết các chi tiết đã nhâp. Bạn có đồng ý?'
                    }
                    onConfirm={() => {
                        setShowAlert(false);
                        setReset(true);
                    }}
                    titleCancel="Bỏ qua"
                    onCancel={() => setShowAlert(false)}
                />
            </WrapperContainerBase>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: Colors.WHITE,
        flex: 1
    },
    scrollView: {
        paddingTop: Mixins.scale(16),
        paddingHorizontal: Mixins.scale(16),
        flexGrow: 1,
        paddingBottom: Mixins.scale(24)
    },
    headTitle: {
        color: Colors.NEUTRALS_200,
        maxWidth: Mixins.scale(300)
    },
    iconLeft: {
        alignSelf: 'center',
        marginRight: Mixins.scale(8),
        tintColor: Colors.BRAND_600,
        transform: [{ rotate: '180deg' }]
    },
    btnArrow: {
        top: 20,
        left: 20,
        right: 20,
        bottom: 20
    },
    componentLeft: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    description: {
        color: Colors.NEUTRALS_500,
        marginTop: Mixins.scale(4)
    },
    storeName: {
        color: Colors.NEUTRALS_100,
        marginTop: Mixins.scale(4)
    },
    storeWrapper: {
        marginHorizontal: Mixins.scale(16),
        marginTop: Mixins.scale(8)
    }
});
export default React.memo(RequestPayment);
