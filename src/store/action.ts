import { apiBase, METHOD } from '@mwg-kits/core';
import { API_CONST } from '@constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
    PaymentApprovalActionTypes,
    CreateTransactionBody,
    UpdateRequestPayBody,
    CancelRequestBody,
    SearchPaymentReqBody
} from 'types';
import { RootReducerType } from './rootReducers';
import { NavigationProp } from '@react-navigation/native';
import Toast from 'react-native-toast-message';

export const STORE_TRANSFER_DATA = 'STORE_TRANSFER_DATA';

export const START_SEARCHING = 'START_SEARCHING';
export const SEARCHING_SUCCESS = 'SEARCHING_SUCCESS';
export const SEARCHING_FAILED = 'SEARCHING_FAILED';

export const START_SEARCHING_EXPENSE_CONTENT =
    'START_SEARCHING_EXPENSE_CONTENT';
export const SEARCHING_EXPENSE_CONTENT_SUCCESS =
    'SEARCHING_EXPENSE_CONTENT_SUCCESS';
export const SEARCHING_EXPENSE_CONTENT_FAILED =
    'SEARCHING_EXPENSE_CONTENT_FAILED';

export const START_REVIEW_MULTI = 'START_REVIEW_MULTI';
export const REVIEW_MULTI_SUCCESS = 'REVIEW_MULTI_SUCCESS';
export const REVIEW_MULTI_FAILED = 'REVIEW_MULTI_FAILED';

export const START_GET_DETAIL_PAYMENT = 'START_GET_DETAIL_PAYMENT';
export const GET_DETAIL_PAYMENT_SUCCESS = 'GET_DETAIL_PAYMENT_SUCCESS';
export const GET_DETAIL_PAYMENT_FAILED = 'GET_DETAIL_PAYMENT_FAILED';

export const START_CREATE_TRANSACTION = 'START_CREATE_TRANSACTION';
export const CREATE_TRANSACTION_SUCCESS = 'CREATE_TRANSACTION_SUCCESS';
export const CREATE_TRANSACTION_FAILED = 'CREATE_TRANSACTION_FAILED';

export const START_UPDATE_REQUEST_PAY = 'START_UPDATE_REQUEST_PAY';
export const UPDATE_REQUEST_PAY_SUCCESS = 'UPDATE_REQUEST_PAY_SUCCESS';
export const UPDATE_REQUEST_PAY_FAILED = 'UPDATE_REQUEST_PAY_FAILED';
export const RESET_UPDATE_REQUEST_PAY = 'RESET_UPDATE_REQUEST_PAY';

export const START_CANCEL_REQUEST = 'START_CANCEL_REQUEST';
export const CANCEL_REQUEST_SUCCESS = 'CANCEL_REQUEST_SUCCESS';
export const CANCEL_REQUEST_FAILED = 'CANCEL_REQUEST_FAILED';

export const START_SEARCH_PAYMENT_REQ = 'START_SEARCH_PAYMENT_REQ';
export const SEARCH_PAYMENT_REQ_SUCCESS = 'SEARCH_PAYMENT_REQ_SUCCESS';
export const SEARCH_PAYMENT_REQ_FAILED = 'SEARCH_PAYMENT_REQ_FAILED';
export const RESET_SEARCH_PAYMENT_REQ_STATE = 'RESET_SEARCH_PAYMENT_REQ_STATE';

export const START_SEARCH_CUSTOMER = 'START_SEARCH_CUSTOMER';
export const SEARCH_CUSTOMER_SUCCESS = 'SEARCH_CUSTOMER_SUCCESS';
export const SEARCH_CUSTOMER_FAILED = 'SEARCH_CUSTOMER_FAILED';
export const getDataFromXwork = (data: any) => {
    return (dispatch: any) => {
        dispatch({
            type: STORE_TRANSFER_DATA,
            data
        });
    };
};

export const search =
    ({
        keyWord,
        isReviewed,
        isShowMore
    }: {
        keyWord: string;
        isReviewed: number;
        isShowMore: number;
    }) =>
    (
        dispatch: (action: PaymentApprovalActionTypes) => void,
        getState: () => RootReducerType
    ) => {
        return (async () => {
            const { userInfo } = getState().commonReducer.xworkData;
            dispatch({
                type: START_SEARCHING
            });
            (global as any).props.showLoader();
            try {
                const token = 'fbdef67c-7057-44d5-bd27-8db5eb3b77aa';
                const body = {
                    keyWord,
                    isReviewed,
                    isShowMore,
                    languageId: 2
                };

                const response = (await apiBase(
                    API_CONST.API_SEARCH,
                    METHOD.POST,
                    body,
                    {
                        access_token: token ?? '',
                        enableLogger: true,
                        userName: userInfo?.userName,
                        _timeout: 30000
                    }
                )) as {
                    object: any;
                    error?: boolean;
                    toastMessage?: string;
                };
                console.log('search response: ', response);
                if (
                    response?.error ||
                    (response?.toastMessage &&
                        response?.toastMessage?.length > 0)
                ) {
                    (global as any).props.hideLoader();
                    Toast.show({
                        type: 'warning',
                        text1: 'Không tìm thấy yêu cầu thanh toán',
                        position: 'bottom'
                    });
                    return dispatch({
                        type: SEARCHING_FAILED,
                        msgError: response?.toastMessage
                    });
                }
                (global as any).props.hideLoader();
                return dispatch({
                    type: SEARCHING_SUCCESS,
                    payload: response.object
                });
            } catch (error: any) {
                (global as any).props.hideLoader();
                console.error('Error in search function:', error);
                Toast.show({
                    type: 'warning',
                    text1: error?.errorReason,
                    position: 'bottom'
                });
                dispatch({
                    type: SEARCHING_FAILED,
                    msgError: error?.errorReason
                });
            }
        })();
    };

export const searchExpenseContent =
    (search?: string) =>
    (
        dispatch: (action: PaymentApprovalActionTypes) => void,
        getState: () => RootReducerType
    ) => {
        return (async () => {
            const { userInfo } = getState().commonReducer.xworkData;
            dispatch({
                type: START_SEARCHING_EXPENSE_CONTENT
            });
            try {
                const token = 'fbdef67c-7057-44d5-bd27-8db5eb3b77aa';
                const body = {
                    keyWord: search ?? null
                };

                const response = (await apiBase(
                    API_CONST.API_SEARCH_EXPENSE_CONTENT,
                    METHOD.POST,
                    body,
                    {
                        access_token: token ?? '',
                        enableLogger: true,
                        userName: userInfo?.userName
                    }
                )) as {
                    object: any;
                    error?: boolean;
                    toastMessage?: string;
                };
                console.log('search expense content response: ', response);
                if (response?.error) {
                    return dispatch({
                        type: SEARCHING_EXPENSE_CONTENT_FAILED,
                        msgError: response?.toastMessage
                    });
                }
                return dispatch({
                    type: SEARCHING_EXPENSE_CONTENT_SUCCESS,
                    payload: response.object
                });
            } catch (error: any) {
                console.error('Error in search function:', error);
                dispatch({
                    type: SEARCHING_EXPENSE_CONTENT_FAILED,
                    msgError: error?.toastMessage
                });
            }
        })();
    };

export const reviewMulti =
    (transactionIdList: string[], actionSearch?: () => void) =>
    (
        dispatch: (action: PaymentApprovalActionTypes) => void,
        getState: () => RootReducerType
    ) => {
        return (async () => {
            const { userInfo } = getState().commonReducer.xworkData;
            dispatch({
                type: START_REVIEW_MULTI
            });
            (global as any).props.showLoader();
            try {
                const token = 'fbdef67c-7057-44d5-bd27-8db5eb3b77aa';
                const body = {
                    transactionIdList
                };

                const response = (await apiBase(
                    API_CONST.API_REVIEW_MULTIPLE,
                    METHOD.POST,
                    body,
                    {
                        access_token: token ?? '',
                        enableLogger: true,
                        userName: userInfo?.userName
                    }
                )) as {
                    object: any;
                    error?: boolean;
                    toastMessage?: string;
                };
                console.log('search expense content response: ', response);
                if (response.object?.transactionErroResponse?.length > 0) {
                    (global as any).props.hideLoader();
                    response?.object?.transactionErroResponse.map(
                        (item: any) => {
                            Toast.show({
                                type: 'error',
                                text1: item?.message,
                                position: 'bottom'
                            });
                            return dispatch({
                                type: REVIEW_MULTI_FAILED,
                                msgError: item?.message
                            });
                        }
                    );
                }
                (global as any).props.hideLoader();
                Toast.show({
                    type: 'success',
                    text1: `Duyệt thành công: ${response.object?.reviewedTranID[0]}`,
                    position: 'bottom'
                });
                if (actionSearch) {
                    actionSearch();
                }
                return dispatch({
                    type: REVIEW_MULTI_SUCCESS,
                    payload: response.object
                });
            } catch (error: any) {
                (global as any).props.hideLoader();
                console.error('Error in search function:', error);
                Toast.show({
                    type: 'error',
                    text1:
                        error?.errorReason ??
                        'Đã có lỗi xảy ra. Vui lòng thử lại sau.',
                    position: 'bottom'
                });
                dispatch({
                    type: REVIEW_MULTI_FAILED,
                    msgError: error?.errorReason
                });
            }
        })();
    };

export const getDetailPayment =
    (transactionId: string) =>
    (
        dispatch: (action: PaymentApprovalActionTypes) => void,
        getState: () => RootReducerType
    ) => {
        return (async () => {
            const { userInfo } = getState().commonReducer.xworkData;
            dispatch({
                type: START_GET_DETAIL_PAYMENT
            });
            try {
                const token = 'fbdef67c-7057-44d5-bd27-8db5eb3b77aa';
                const body = {
                    transactionId
                };

                const response = (await apiBase(
                    API_CONST.API_GET_DETAIL,
                    METHOD.POST,
                    body,
                    {
                        access_token: token ?? '',
                        enableLogger: true,
                        userName: userInfo?.userName
                    }
                )) as {
                    object: any;
                    error?: boolean;
                    toastMessage?: string;
                };
                console.log('search expense content response: ', response);

                if (response?.error) {
                    return dispatch({
                        type: GET_DETAIL_PAYMENT_FAILED,
                        msgError: response?.toastMessage
                    });
                }
                return dispatch({
                    type: GET_DETAIL_PAYMENT_SUCCESS,
                    payload: response.object
                });
            } catch (error: any) {
                console.error('Error in search function:', error);
                dispatch({
                    type: GET_DETAIL_PAYMENT_FAILED,
                    msgError: error?.errorReason
                });
            }
        })();
    };

export const createTransaction =
    (body: CreateTransactionBody) =>
    (
        dispatch: (action: PaymentApprovalActionTypes) => void,
        getState: () => RootReducerType
    ) => {
        return (async () => {
            const { userInfo } = getState().commonReducer.xworkData;
            dispatch({
                type: START_CREATE_TRANSACTION
            });
            (global as any).props.showLoader();
            try {
                const token = 'fbdef67c-7057-44d5-bd27-8db5eb3b77aa';
                const Body = {
                    transactionObject: body.transactionObject,
                    expenseContentId: body.expenseContentId,
                    expenseContentName: body.expenseContentName,
                    storeLogin: userInfo?.storeId
                };

                const response = (await apiBase(
                    API_CONST.API_CREATE_TRANSACTION,
                    METHOD.POST,
                    Body,
                    {
                        access_token: token ?? '',
                        enableLogger: true,
                        userName: userInfo?.userName
                    }
                )) as {
                    object: any;
                    error?: boolean;
                    toastMessage?: string;
                };
                console.log('search expense content response: ', response);
                if (response?.error) {
                    (global as any).props.hideLoader();
                    Toast.show({
                        type: 'error',
                        text1: response?.toastMessage,
                        position: 'bottom'
                    });
                    return dispatch({
                        type: CREATE_TRANSACTION_FAILED,
                        msgError: response?.toastMessage
                    });
                }
                (global as any).props.hideLoader();
                Toast.show({
                    type: 'success',
                    text1: 'Tạo yêu cầu thành công.',
                    position: 'bottom'
                });
                return dispatch({
                    type: CREATE_TRANSACTION_SUCCESS,
                    payload: response.object
                });
            } catch (error: any) {
                (global as any).props.hideLoader();
                console.error('Error in search function:', error);
                Toast.show({
                    type: 'error',
                    text1: error?.errorReason,
                    position: 'bottom'
                });
                dispatch({
                    type: CREATE_TRANSACTION_FAILED,
                    msgError: error?.errorReason
                });
            }
        })();
    };

export const updateRequestPay =
    (
        body: UpdateRequestPayBody,
        navigation: NavigationProp<any>,
        actionSearch: () => void
    ) =>
    (
        dispatch: (action: PaymentApprovalActionTypes) => void,
        getState: () => RootReducerType
    ) => {
        return (async () => {
            const { userInfo } = getState().commonReducer.xworkData;
            dispatch({
                type: START_UPDATE_REQUEST_PAY
            });
            (global as any).props.showLoader();
            try {
                const token = 'fbdef67c-7057-44d5-bd27-8db5eb3b77aa';
                const Body = {
                    transactionObject: body.transactionObject,
                    expenseContentId: body.expenseContentId,
                    expenseContentName: body.expenseContentName,
                    storeLogin: Number(userInfo?.storeId)
                };

                const response = (await apiBase(
                    API_CONST.API_UPDATE_REQUEST_PAY,
                    METHOD.POST,
                    Body,
                    {
                        access_token: token ?? '',
                        enableLogger: true,
                        userName: userInfo?.userName
                    }
                )) as {
                    object: any;
                    error?: boolean;
                    toastMessage?: string;
                };
                console.log('update response: ', response);
                if (response?.error) {
                    (global as any).props.hideLoader();
                    Toast.show({
                        type: 'error',
                        text1: response?.toastMessage,
                        position: 'bottom'
                    });
                    return dispatch({
                        type: UPDATE_REQUEST_PAY_FAILED,
                        msgError: response?.toastMessage
                    });
                }
                (global as any).props.hideLoader();
                Toast.show({
                    type: 'success',
                    text1: 'Cập nhật thành công',
                    position: 'bottom'
                });
                if (actionSearch) {
                    actionSearch();
                }
                if (navigation) {
                    navigation.goBack();
                }
                return dispatch({
                    type: UPDATE_REQUEST_PAY_SUCCESS,
                    payload: response.object
                });
            } catch (error: any) {
                console.error('Error in update function:', error);
                (global as any).props.hideLoader();
                Toast.show({
                    type: 'error',
                    text1: error?.errorReason,
                    position: 'bottom'
                });
                dispatch({
                    type: UPDATE_REQUEST_PAY_FAILED,
                    msgError: error?.errorReason
                });
            }
        })();
    };

export const cancelRequest =
    (body: CancelRequestBody, navigation?: NavigationProp<any>) =>
    (
        dispatch: (action: PaymentApprovalActionTypes) => void,
        getState: () => RootReducerType
    ) => {
        return (async () => {
            const { userInfo } = getState().commonReducer.xworkData;
            dispatch({
                type: START_CANCEL_REQUEST
            });
            (global as any).props.showLoader();
            try {
                const token = 'fbdef67c-7057-44d5-bd27-8db5eb3b77aa';
                const Body = {
                    transactionList: body.transactionList,
                    storeLogin: Number(userInfo?.storeId)
                };

                const response = (await apiBase(
                    API_CONST.API_CANCEL_REQUEST,
                    METHOD.POST,
                    Body,
                    {
                        access_token: token ?? '',
                        enableLogger: true,
                        userName: userInfo?.userName
                    }
                )) as {
                    object: any;
                    error?: boolean;
                    toastMessage?: string;
                };
                console.log('search expense content response: ', response);
                if (response?.error) {
                    (global as any).props.hideLoader();
                    Toast.show({
                        type: 'error',
                        text1: response?.toastMessage,
                        position: 'bottom'
                    });
                    return dispatch({
                        type: CANCEL_REQUEST_FAILED,
                        msgError: response?.toastMessage
                    });
                }
                (global as any).props.hideLoader();
                Toast.show({
                    type: 'success',
                    text1: response?.object,
                    position: 'bottom'
                });
                if (navigation) {
                    navigation.goBack();
                }

                return dispatch({
                    type: CANCEL_REQUEST_SUCCESS,
                    payload: response.object
                });
            } catch (error: any) {
                (global as any).props.hideLoader();
                console.error('Error in search function:', error);
                Toast.show({
                    type: 'error',
                    text1: error?.errorReason,
                    position: 'bottom'
                });
                dispatch({
                    type: CANCEL_REQUEST_FAILED,
                    msgError: error?.errorReason
                });
            }
        })();
    };

export const searchPaymentReq =
    (Body: SearchPaymentReqBody) =>
    (
        dispatch: (action: PaymentApprovalActionTypes) => void,
        getState: () => RootReducerType
    ) => {
        return (async () => {
            const { userInfo } = getState().commonReducer.xworkData;
            dispatch({
                type: START_SEARCH_PAYMENT_REQ
            });
            (global as any).props.showLoader();
            try {
                const token = 'fbdef67c-7057-44d5-bd27-8db5eb3b77aa';
                const body = {
                    fromDate: Body.fromDate,
                    toDate: Body.toDate,
                    isReviewed: Body.isReviewed,
                    isLock: Body.isLock,
                    keyWord: Body.keyWord ?? null
                };
                const response = (await apiBase(
                    API_CONST.API_SEARCH_PAYMENT_REQ,
                    METHOD.POST,
                    body,
                    {
                        access_token: token ?? '',
                        enableLogger: true,
                        userName: userInfo?.userName
                    }
                )) as {
                    object: any;
                    error?: boolean;
                    toastMessage?: string;
                };
                console.log('search expense content response: ', response);
                if (
                    response?.error ||
                    (response?.toastMessage &&
                        response?.toastMessage?.length > 0)
                ) {
                    (global as any).props.hideLoader();
                    Toast.show({
                        type: 'warning',
                        text1: 'Không tìm thấy yêu cầu thanh toán',
                        position: 'bottom'
                    });
                    return dispatch({
                        type: SEARCH_PAYMENT_REQ_FAILED,
                        msgError: response?.toastMessage
                    });
                }
                (global as any).props.hideLoader();
                return dispatch({
                    type: SEARCH_PAYMENT_REQ_SUCCESS,
                    payload: response.object
                });
            } catch (error: any) {
                (global as any).props.hideLoader();
                console.error('Error in search function:', error);
                Toast.show({
                    type: 'warning',
                    text1: error?.errorReason,
                    position: 'bottom'
                });
                dispatch({
                    type: SEARCH_PAYMENT_REQ_FAILED,
                    msgError: error?.errorReason
                });
            }
        })();
    };
export const resetSearchPaymentReqState = () => {
    return (dispatch: (action: PaymentApprovalActionTypes) => void) => {
        dispatch({
            type: RESET_SEARCH_PAYMENT_REQ_STATE
        });
    };
};

export const resetUpdateRequestState = () => {
    return (dispatch: (action: PaymentApprovalActionTypes) => void) => {
        dispatch({
            type: RESET_UPDATE_REQUEST_PAY
        });
    };
};

export const searchCustomer =
    (key: string) =>
    (
        dispatch: (action: PaymentApprovalActionTypes) => void,
        getState: () => RootReducerType
    ) => {
        return (async () => {
            const { userInfo } = getState().commonReducer.xworkData;
            dispatch({
                type: START_SEARCH_CUSTOMER
            });
            try {
                const token = 'fbdef67c-7057-44d5-bd27-8db5eb3b77aa';
                const body = {
                    customerName: key
                };
                const response = (await apiBase(
                    API_CONST.API_SEARCH_CUSTOMER,
                    METHOD.POST,
                    body,
                    {
                        access_token: token ?? '',
                        enableLogger: true,
                        userName: userInfo?.userName
                    }
                )) as {
                    object: any;
                    error?: boolean;
                    toastMessage?: string;
                };
                console.log('search customer response: ', response);
                if (response?.error) {
                    return dispatch({
                        type: SEARCH_CUSTOMER_FAILED,
                        msgError: response?.toastMessage
                    });
                }

                return dispatch({
                    type: SEARCH_CUSTOMER_SUCCESS,
                    payload: response.object
                });
            } catch (error: any) {
                console.error('Error in search customer:', error);
                dispatch({
                    type: SEARCH_CUSTOMER_FAILED,
                    msgError: error?.toastMessage
                });
            }
        })();
    };
