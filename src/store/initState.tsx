import {
    SearchRes,
    SearchExpenseContent,
    IStandardReducerState,
    ReviewMulti,
    GetDetailPayment,
    SearchPaymentReq,
    SearchCustomer
} from 'types';

export interface IInitialState {
    xworkData: any;
    search: IStandardReducerState<SearchRes[]>;
    searchExpenseContent: IStandardReducerState<SearchExpenseContent[]>;
    reviewMulti: IStandardReducerState<ReviewMulti>;
    getDetailPayment: IStandardReducerState<GetDetailPayment>;
    createTransaction: IStandardReducerState<string>;
    updateRequestPay: IStandardReducerState<string>;
    cancelRequest: IStandardReducerState<any>;
    searchPaymentReq: IStandardReducerState<SearchPaymentReq[]>;
    searchCustomer: IStandardReducerState<SearchCustomer[]>;
}

export const initialState: IInitialState = {
    xworkData: [],
    search: {
        isFetching: false,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: []
    },
    searchExpenseContent: {
        isFetching: false,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: []
    },
    reviewMulti: {
        isFetching: false,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: undefined
    },
    getDetailPayment: {
        isFetching: false,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: undefined
    },
    createTransaction: {
        isFetching: false,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: undefined
    },
    updateRequestPay: {
        isFetching: false,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: undefined
    },
    cancelRequest: {
        isFetching: false,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: undefined
    },
    searchPaymentReq: {
        isFetching: false,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: undefined
    },
    searchCustomer: {
        isFetching: false,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: undefined
    }
};
