import * as _state from './initState';
import * as _action from './action';
import { PaymentApprovalActionTypes } from 'types';
const commonReducer = function (state = _state.initialState, action: any) {
    switch (action.type) {
        case _action.STORE_TRANSFER_DATA:
            return {
                ...state,
                xworkData: action.data
            };

        default:
            return state;
    }
};

const paymentApprovalReducer = (
    state: _state.IInitialState = _state.initialState,
    action: PaymentApprovalActionTypes
): _state.IInitialState => {
    switch (action.type) {
        //search
        case _action.START_SEARCHING:
            return {
                ...state,
                search: {
                    isFetching: true,
                    isSuccess: false,
                    isError: false,
                    msgError: '',
                    data: []
                }
            };
        case _action.SEARCHING_SUCCESS:
            return {
                ...state,
                search: {
                    isFetching: false,
                    isSuccess: true,
                    isError: false,
                    msgError: '',
                    data: action.payload
                }
            };
        case _action.SEARCHING_FAILED:
            return {
                ...state,
                search: {
                    isFetching: false,
                    isSuccess: false,
                    isError: true,
                    msgError: action.msgError,
                    data: []
                }
            };

        //search expense content
        case _action.START_SEARCHING_EXPENSE_CONTENT:
            return {
                ...state,
                searchExpenseContent: {
                    isFetching: true,
                    isSuccess: false,
                    isError: false,
                    msgError: '',
                    data: []
                }
            };
        case _action.SEARCHING_EXPENSE_CONTENT_SUCCESS:
            return {
                ...state,
                searchExpenseContent: {
                    isFetching: false,
                    isSuccess: true,
                    isError: false,
                    msgError: '',
                    data: action.payload
                }
            };
        case _action.SEARCHING_EXPENSE_CONTENT_FAILED:
            return {
                ...state,
                searchExpenseContent: {
                    isFetching: false,
                    isSuccess: false,
                    isError: true,
                    msgError: action.msgError,
                    data: []
                }
            };

        //review multi
        case _action.START_REVIEW_MULTI:
            return {
                ...state,
                reviewMulti: {
                    isFetching: true,
                    isSuccess: false,
                    isError: false,
                    msgError: '',
                    data: undefined
                }
            };
        case _action.REVIEW_MULTI_SUCCESS:
            return {
                ...state,
                reviewMulti: {
                    isFetching: false,
                    isSuccess: true,
                    isError: false,
                    msgError: '',
                    data: action.payload
                }
            };
        case _action.REVIEW_MULTI_FAILED:
            return {
                ...state,
                reviewMulti: {
                    isFetching: false,
                    isSuccess: false,
                    isError: true,
                    msgError: action.msgError,
                    data: undefined
                }
            };

        //get detail payment
        case _action.START_GET_DETAIL_PAYMENT:
            return {
                ...state,
                getDetailPayment: {
                    isFetching: true,
                    isSuccess: false,
                    isError: false,
                    msgError: '',
                    data: undefined
                }
            };
        case _action.GET_DETAIL_PAYMENT_SUCCESS:
            return {
                ...state,
                getDetailPayment: {
                    isFetching: false,
                    isSuccess: true,
                    isError: false,
                    msgError: '',
                    data: action.payload
                }
            };
        case _action.GET_DETAIL_PAYMENT_FAILED:
            return {
                ...state,
                getDetailPayment: {
                    isFetching: false,
                    isSuccess: false,
                    isError: true,
                    msgError: action.msgError,
                    data: undefined
                }
            };

        //create transaction
        case _action.START_CREATE_TRANSACTION:
            return {
                ...state,
                createTransaction: {
                    isFetching: true,
                    isSuccess: false,
                    isError: false,
                    msgError: '',
                    data: undefined
                }
            };
        case _action.CREATE_TRANSACTION_SUCCESS:
            return {
                ...state,
                createTransaction: {
                    isFetching: false,
                    isSuccess: true,
                    isError: false,
                    msgError: '',
                    data: action.payload
                }
            };
        case _action.CREATE_TRANSACTION_FAILED:
            return {
                ...state,
                createTransaction: {
                    isFetching: false,
                    isSuccess: false,
                    isError: true,
                    msgError: action.msgError,
                    data: undefined
                }
            };

        //update request pay
        case _action.START_UPDATE_REQUEST_PAY:
            return {
                ...state,
                updateRequestPay: {
                    isFetching: true,
                    isSuccess: false,
                    isError: false,
                    msgError: '',
                    data: undefined
                }
            };
        case _action.UPDATE_REQUEST_PAY_SUCCESS:
            return {
                ...state,
                updateRequestPay: {
                    isFetching: false,
                    isSuccess: true,
                    isError: false,
                    msgError: '',
                    data: action.payload
                }
            };
        case _action.UPDATE_REQUEST_PAY_FAILED:
            return {
                ...state,
                updateRequestPay: {
                    isFetching: false,
                    isSuccess: false,
                    isError: true,
                    msgError: action.msgError,
                    data: undefined
                }
            };
        case _action.RESET_UPDATE_REQUEST_PAY:
            return {
                ...state,
                updateRequestPay: {
                    isFetching: false,
                    isSuccess: false,
                    isError: false,
                    msgError: '',
                    data: undefined
                }
            };

        //cancel request
        case _action.START_CANCEL_REQUEST:
            return {
                ...state,
                cancelRequest: {
                    isFetching: true,
                    isSuccess: false,
                    isError: false,
                    msgError: '',
                    data: undefined
                }
            };
        case _action.CANCEL_REQUEST_SUCCESS:
            return {
                ...state,
                cancelRequest: {
                    isFetching: false,
                    isSuccess: true,
                    isError: false,
                    msgError: '',
                    data: action.payload
                }
            };
        case _action.CANCEL_REQUEST_FAILED:
            return {
                ...state,
                cancelRequest: {
                    isFetching: false,
                    isSuccess: false,
                    isError: true,
                    msgError: action.msgError,
                    data: undefined
                }
            };

        //search payment request
        case _action.START_SEARCH_PAYMENT_REQ:
            return {
                ...state,
                searchPaymentReq: {
                    isFetching: true,
                    isSuccess: false,
                    isError: false,
                    msgError: '',
                    data: undefined
                }
            };
        case _action.SEARCH_PAYMENT_REQ_SUCCESS:
            return {
                ...state,
                searchPaymentReq: {
                    isFetching: false,
                    isSuccess: true,
                    isError: false,
                    msgError: '',
                    data: action.payload
                }
            };
        case _action.SEARCH_PAYMENT_REQ_FAILED:
            return {
                ...state,
                searchPaymentReq: {
                    isFetching: false,
                    isSuccess: false,
                    isError: true,
                    msgError: action.msgError,
                    data: undefined
                }
            };
        case _action.RESET_SEARCH_PAYMENT_REQ_STATE:
            return {
                ...state,
                searchPaymentReq: {
                    isFetching: false,
                    isSuccess: false,
                    isError: false,
                    msgError: '',
                    data: undefined
                }
            };

        //search customer
        case _action.START_SEARCH_CUSTOMER:
            return {
                ...state,
                searchCustomer: {
                    isFetching: true,
                    isSuccess: false,
                    isError: false,
                    msgError: '',
                    data: undefined
                }
            };
        case _action.SEARCH_CUSTOMER_SUCCESS:
            return {
                ...state,
                searchCustomer: {
                    isFetching: false,
                    isSuccess: true,
                    isError: false,
                    msgError: '',
                    data: action.payload
                }
            };
        case _action.SEARCH_CUSTOMER_FAILED:
            return {
                ...state,
                searchCustomer: {
                    isFetching: false,
                    isSuccess: false,
                    isError: true,
                    msgError: action.msgError,
                    data: undefined
                }
            };
        default:
            return state;
    }
};

export { commonReducer, paymentApprovalReducer };
