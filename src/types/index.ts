import {
    SEARCHING_SUCCESS,
    SEARCHING_FAILED,
    START_SEARCHING,
    START_SEARCHING_EXPENSE_CONTENT,
    SEARCHING_EXPENSE_CONTENT_FAILED,
    SEARCHING_EXPENSE_CONTENT_SUCCESS,
    START_REVIEW_MULTI,
    REVIEW_MULTI_SUCCESS,
    REVIEW_MULTI_FAILED,
    START_GET_DETAIL_PAYMENT,
    GET_DETAIL_PAYMENT_FAILED,
    GET_DETAIL_PAYMENT_SUCCESS,
    START_CREATE_TRANSACTION,
    CREATE_TRANSACTION_FAILED,
    CREATE_TRANSACTION_SUCCESS,
    START_UPDATE_REQUEST_PAY,
    UPDATE_REQUEST_PAY_FAILED,
    UPDATE_REQUEST_PAY_SUCCESS,
    START_CANCEL_REQUEST,
    CANCEL_REQUEST_FAILED,
    CANCEL_REQUEST_SUCCESS,
    START_SEARCH_PAYMENT_REQ,
    SEARCH_PAYMENT_REQ_FAILED,
    SEARCH_PAYMENT_REQ_SUCCESS,
    RESET_SEARCH_PAYMENT_REQ_STATE,
    RESET_UPDATE_REQUEST_PAY,
    START_SEARCH_CUSTOMER,
    SEARCH_CUSTOMER_FAILED,
    SEARCH_CUSTOMER_SUCCESS
} from './../store/action';

export interface IStandardReducerState<T> {
    isFetching: boolean;
    isSuccess: boolean;
    isError: boolean;
    msgError: string;
    data?: T;
}

export type SearchRes = {
    dateCreate: number;
    transactionTypeID: number | null;
    userInitialReviewName: string | null;
    intialReviewedDate: number | null;
    initialReviewedDate: number | null;
    userPostFullname: string | null;
    requestUserName: string;
    paymentName: string | null;
    userInititalReviewName: string | null;
    userReviewName: string | null;
    userInOutComeName: string | null;
    expenseContentName: string | null;
    storeName: string | null;
    expiredDate: number | null;
    isHasAttachment: boolean | null;
    reviewedDate: number | null;
    isDelete: boolean | null;
    totalAmount: number;
    storeId: number | null;
    isLock: boolean | null;
    statusName: string | null;
    content: string;
    transactionID: string;
    datePost: number | null;
    receiveMoneyName: string | null;
    voucherCode: string | null;
    isReview: number | null;
    receiveMoneyUsername: string | null;
    paymentReasons: string | null;
    isPost: boolean | null;
    userInOutCome: string | null;
    isInitialReview: number;
    latePaymentReason: string | null;
    voucherReceivedDate: number | null;
    userRequest: string;
    transactionAttachmentEntityList: Attachment[];
    userInitialReview: string;
    userReview: string;
    paymentDate: string;
};

export type SearchExpenseContent = {
    isRequiredInvoice: number;
    isHavingInvoice: number;
    analysisId: null;
    expenseContentName: string;
    isDelete: null;
    userDelete: null;
    dateDelete: null;
    description: string;
    expenseContentId: string;
};

export type ReviewMulti = {
    reviewedTranID: string[];
    transactionErroResponse: [
        {
            transactionId: string;
            message: string;
        }
    ];
};

export type TransactionDetailEntity = {
    customerID: number;
    customerName: string;
    customerAddress: string;
    customerTax: string;
    invoiceID: string;
    invoiceSymbol: string;
    denominator: string;
    invoiceDate: string;
    totalAmount: number;
    VAT: number;
    content: string;
    [key: string]: any;
};

export type TransactionObjectType = {
    transactionDetailEntities: TransactionDetailEntity[];
    base64Attachments: string[];
    totalAmount: number;
};

export type TransactionObject = {
    transactionTypeId: number;
    expenseContentId: number;
    expenseContentName: string;
    voucherPlace: number;
    payableTypeId: number;
    departmentId: number;
    userCreate: string;
    dateCreate: number;
    userRequest: string;
    storeId: number;
    customerId: number;
    customerName: string;
    customerPhone: string | null;
    customerAddress: string;
    customerTax: string | null;
    content: string | null;
    totalAdvance: number;
    totalAmountBf: number;
    totalAmountBfVnd: number;
    totalVat: number;
    totalAmount: number;
    totalAmountRemain: number;
    currencyUnitId: number;
    currencyExchange: number;
    voucherDate: number;
    receiveMoneyCustomerId: number;
    receiveMoneyName: string;
    receiveMoneyIdCard: string;
    receiveMoneyAddress: string | null;
    voucherCode: string | null;
    isReview: number;
    lastReviewTime: number | null;
    isInOutCome: number;
    userInOutCome: string | null;
    dateInOutCome: number | null;
    isPost: number;
    userPost: string | null;
    datePost: number | null;
    isLock: number;
    userLock: string | null;
    dateLock: number | null;
    isDelete: number;
    userDelete: string | null;
    dateDelete: number | null;
    defaultAccountId: number;
    isPrivate: number;
    paymentOrderId: number;
    orderToPayId: number;
    redundantMoney: number;
    companyId: number;
    isCreateFromPaymentOrder: number;
    isDebtAccount: number;
    paymentOrderTransactionId: number;
    receiveMoneyUsername: string;
    createdByOtherApps: number;
    isInitialReview: number;
    paymentReasons: string | null;
    paymentDate: number | null;
    latePaymentReason: string | null;
    voucherReceivedDate: number | null;
    transactionId: string;
    defaultReview: string | null;
    voucherMonth: string | null;
    transactionDetailEntities: DetailEntity[];
    transactionRelationEntities: null;
    userReviewEntities: [];
    postBookEntities: null;
    transactionAttachmentEntities: Attachment[];
    lstImageBase64String: string | null;
    companyName: string | null;
    paymentOrderTransactionEntity: null;
    paymentReqId: number;
    bankId: number;
    beneficiaryId: number;
    cmdInfoCurrencyUnitId: number;
    isHasAttachment: number;
    isPayment: number;
    invoiceId: string | null;
    invoiceSymbol: string | null;
    invoiceDate: number | null;
    guid: string | null;
    cus_TransactionNote: string | null;
    isNotOutCome: number;
    paymentReqIds: string | null;
    transactionMessageEntities: [];
    transactionInfoEntity: {
        transactionId: string | null;
        voucherReceivedDate: number | null;
        latePaymentReason: string | null;
        inputTime: number | null;
    };
    receiveMoneyType: number;
    base64Attachments: string | null;
    vat: number;
};

export type Attachment = {
    attachmentID: string;
    transactionID: string;
    attachmentName: string | null;
    description: string;
    fileType: string;
    fileURL: string;
    attachmentFileName: string;
    dateCreate: Date | null;
    status: number;
};

export type DetailEntity = {
    transactionDetailID: string;
    transactionID: string | null;
    customerID: number;
    customerName: string;
    customerAddress: string;
    customerPhone: string | null;
    customerTax: string;
    departmentID: number;
    invoiceID: string;
    invoiceSymbol: string;
    invoiceDate: number;
    denominator: string;
    contractCode: string | null;
    content: string;
    currencyUnitID: number;
    currencyExchange: number;
    totalAmountBF: number;
    totalAmountBFVND: number;
    VAT: number;
    totalVAT: number;
    totalAmount: number;
    isDelete: boolean;
    userDelete: string | null;
    dateDelete: number | null;
    relateVoucherID: string | null;
    voucherConcern: string | null;
    expenseType: string | null;
    inputTime: number | null;
    expenseContentID: number;
    typeID: number;
    isExist: boolean;
    isSelected: boolean;
    isEdited: boolean;
    currencyUnitName: string | null;
    currencyUnitExchange: string | null;
    transactionDetailInvoice: string | null;
    storeID: number;
    status: number;
    totalAmountAt: number;
    oldTotalAmount: number;
    oldVAT: number;
    storeId: number;
    exist: boolean;
    delete: boolean;
    selected: boolean;
    vat: number;
    edited: boolean;
};

export type GetDetailPayment = {
    transactionObject: TransactionObject;
    expenseContentId: string;
    expenseContentName: string | null;
};

export type CreateTransactionBody = {
    transactionObject: TransactionObjectType;
    expenseContentId: number;
    expenseContentName: string;
};

export type UpdateRequestPayBody = {
    transactionObject: TransactionObject;
    expenseContentId: number;
    expenseContentName: string | null;
};

export type CancelRequestBody = {
    transactionList: TransactionListItem[];
};

export type TransactionListItem = {
    transactionID: string;
    transactionTypeID: number;
    isLock: number;
    isDelete: number;
    isReview: number;
    isInitialReview: number;
};

export type SearchPaymentReqBody = {
    fromDate: string;
    toDate: string;
    isReviewed: number;
    isLock: number;
    keyWord?: string | null;
};

export type SearchPaymentReq = {
    dateCreate: number;
    transactionTypeID: number;
    userInitialReviewName: string | null;
    intialReviewedDate: string | null;
    requestUserName: string;
    paymentName: string | null;
    userInititalReviewName: string | null;
    userReviewName: string | null;
    userInOutComeName: string | null;
    userPostFullname: string | null;
    expiredDate: string | null;
    storeName: string | null;
    isDelete: number;
    reviewedDate: string | null;
    expenseContentName: string;
    totalAmount: number;
    storeId: string | null;
    isLock: number;
    statusName: string | null;
    datePost: string | null;
    isReview: number;
    isPost: number | null;
    isHasAttachment: boolean | null;
    isInitialReview: number;
    receiveMoneyName: string | null;
    voucherCode: string | null;
    userInOutCome: string | null;
    receiveMoneyUsername: string | null;
    paymentReasons: string | null;
    latePaymentReason: string | null;
    voucherReceivedDate: string | null;
    transactionID: string;
    content: string | null;
};

export type SearchCustomer = {
    contactemail: string | null;
    siccode: string | null;
    taxcustomername: string;
    customeraddress: string;
    customerphonenum: string | null;
    isdeleted: boolean | null;
    deleteduser: string | null;
    customertypeid: number;
    taxid: string;
    annualrevenue: number | null;
    mobilephone: string | null;
    brankname: string | null;
    logo: string | null;
    internalusername: string | null;
    isdefault: number;
    fax: string | null;
    updateduser: string | null;
    bussinesssegmentid: number | null;
    contactaddress: string | null;
    getablenumday: number | null;
    userdelete: string | null;
    deleteddate: Date | null;
    goutpuserassignedid: string | null;
    countryid: number;
    customerparentid: number;
    payablenumday: number | null;
    customerid: number;
    datedelete: Date | null;
    employees: number | null;
    issystem: boolean | null;
    birthdare: Date | null;
    customeridcard: string | null;
    createddate: Date | null;
    isactive: number;
    description: string | null;
    userassigned: string | null;
    issupplier: boolean;
    contactname: string | null;
    clauseinfo: string | null;
    payfunctiontype: string | null;
    indystryid: number | null;
    brankaccount: string | null;
    inputtime: Date | null;
    contactphone: string | null;
    customername: string;
    isusedbyaccountant: number;
    islockpurchaseorder: number;
    website: string | null;
    sex: number;
    createduser: string | null;
    payabledebtlimit: number | null;
    islockoutputreturn: number;
    brankaddress: string | null;
    recordid: string | null;
    customeremail: string | null;
    islockinput: number;
    ownership: string | null;
    otheremail: string | null;
    updateddate: Date | null;
    customercode: string | null;
    isdelete: boolean | null;
    getabledebtlimit: number | null;
    anniversary: Date | null;
};

type SearchingSuccessAction = {
    type: typeof SEARCHING_SUCCESS;
    payload: SearchRes[];
};

type StartSearchingAction = {
    type: typeof START_SEARCHING;
};

type SearchingFailedAction = {
    type: typeof SEARCHING_FAILED;
    msgError: string | any;
};

type StartSearchingExpenseContentAction = {
    type: typeof START_SEARCHING_EXPENSE_CONTENT;
};

type SearchingExpenseContentSuccessAction = {
    type: typeof SEARCHING_EXPENSE_CONTENT_SUCCESS;
    payload: SearchExpenseContent[];
};

type SearchingExpenseContentAction = {
    type: typeof SEARCHING_EXPENSE_CONTENT_FAILED;
    msgError: string | any;
};

type StartReviewMultiAction = {
    type: typeof START_REVIEW_MULTI;
};

type ReviewMultiSuccessAction = {
    type: typeof REVIEW_MULTI_SUCCESS;
    payload: ReviewMulti;
};

type ReviewMultiFailedAction = {
    type: typeof REVIEW_MULTI_FAILED;
    msgError: string | any;
};

type StartGetDetailPaymentAction = {
    type: typeof START_GET_DETAIL_PAYMENT;
};

type GetDetailPaymentSuccessAction = {
    type: typeof GET_DETAIL_PAYMENT_SUCCESS;
    payload: GetDetailPayment;
};

type GetDetailPaymentFailedAction = {
    type: typeof GET_DETAIL_PAYMENT_FAILED;
    msgError: string | any;
};

type StartCreateTransactionAction = {
    type: typeof START_CREATE_TRANSACTION;
};

type CreateTransactionSuccessAction = {
    type: typeof CREATE_TRANSACTION_SUCCESS;
    payload: string;
};

type CreateTransactionFailedAction = {
    type: typeof CREATE_TRANSACTION_FAILED;
    msgError: string | any;
};

type StartUpdateRequestPayAction = {
    type: typeof START_UPDATE_REQUEST_PAY;
};

type UpdateRequestPaySuccessAction = {
    type: typeof UPDATE_REQUEST_PAY_SUCCESS;
    payload: string;
};

type UpdateRequestPayFailedAction = {
    type: typeof UPDATE_REQUEST_PAY_FAILED;
    msgError: string | any;
};

type StartCancelRequestAction = {
    type: typeof START_CANCEL_REQUEST;
};

type CancelRequestSuccessAction = {
    type: typeof CANCEL_REQUEST_SUCCESS;
    payload: any;
};

type CancelRequestFailedAction = {
    type: typeof CANCEL_REQUEST_FAILED;
    msgError: string | any;
};

type StartSearchPaymentReqAction = {
    type: typeof START_SEARCH_PAYMENT_REQ;
};

type SearchPaymentReqSuccessAction = {
    type: typeof SEARCH_PAYMENT_REQ_SUCCESS;
    payload: SearchPaymentReq[];
};

type SearchPaymentReqFailedAction = {
    type: typeof SEARCH_PAYMENT_REQ_FAILED;
    msgError: string | any;
};

type ResetSearchPaymentReqStateAction = {
    type: typeof RESET_SEARCH_PAYMENT_REQ_STATE;
};

type ResetUpdateRequestPayStateAction = {
    type: typeof RESET_UPDATE_REQUEST_PAY;
};

type StartSearchCustomerAction = {
    type: typeof START_SEARCH_CUSTOMER;
};

type SearchCustomerSuccessAction = {
    type: typeof SEARCH_CUSTOMER_SUCCESS;
    payload: SearchCustomer[];
};

type SearchCustomerFailedAction = {
    type: typeof SEARCH_CUSTOMER_FAILED;
    msgError: string | any;
};

export type PaymentApprovalActionTypes =
    | StartSearchingAction
    | SearchingFailedAction
    | SearchingSuccessAction
    | StartSearchingExpenseContentAction
    | SearchingExpenseContentSuccessAction
    | SearchingExpenseContentAction
    | StartReviewMultiAction
    | ReviewMultiSuccessAction
    | ReviewMultiFailedAction
    | StartGetDetailPaymentAction
    | GetDetailPaymentFailedAction
    | GetDetailPaymentSuccessAction
    | StartCreateTransactionAction
    | CreateTransactionFailedAction
    | CreateTransactionSuccessAction
    | StartUpdateRequestPayAction
    | UpdateRequestPayFailedAction
    | UpdateRequestPaySuccessAction
    | StartCancelRequestAction
    | CancelRequestFailedAction
    | CancelRequestSuccessAction
    | StartSearchPaymentReqAction
    | SearchPaymentReqFailedAction
    | SearchPaymentReqSuccessAction
    | ResetSearchPaymentReqStateAction
    | ResetUpdateRequestPayStateAction
    | StartSearchCustomerAction
    | SearchCustomerSuccessAction
    | SearchCustomerFailedAction;
