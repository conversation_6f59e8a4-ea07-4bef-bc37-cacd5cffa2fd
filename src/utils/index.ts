import moment from 'moment';

export const formattedPrice = (
    price: number,
    symbol = true,
    currency = '₫'
): string => {
    const formattedNumber = new Intl.NumberFormat('vi-VN').format(
        price > 0 ? Math.trunc(price) : 0
    );
    if (symbol) {
        return formattedNumber + currency;
    }
    return formattedNumber;
};

export const formatDate = (isoString: string) => {
    const date = new Date(isoString);
    return date.toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        timeZone: 'Asia/Ho_Chi_Minh'
    });
};

export const convertToVietnamTime = (isoString: string) => {
    const date = new Date(isoString);
    const vietnamTime = new Date(date.getTime() + 7 * 60 * 60 * 1000);
    return vietnamTime.toISOString();
};

export const formatDateTime = (timestamp: number) => {
    const date = new Date(timestamp);

    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Month is 0-based
    const year = date.getFullYear();

    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${day}/${month}/${year} ${hours}:${minutes}`;
};

export function formatTimestamp(timestamp: number): string {
    const now = moment();
    const date = moment(timestamp);

    const formattedTime = date.format('HH:mm');

    if (date.isSame(now, 'day')) {
        return `${formattedTime}, Hôm nay`;
    }

    if (date.isSame(now.clone().subtract(1, 'day'), 'day')) {
        return `${formattedTime}, Hôm qua`;
    }

    if (date.isSame(now.clone().add(1, 'day'), 'day')) {
        return `${formattedTime}, Ngày mai`;
    }

    return `${formattedTime}, ${date.format('DD/MM/YYYY')}`;
}

export const getPrevious7Days = (dateStr: string): string => {
    dateStr = dateStr.replace(/-/g, '/');
    const [day, month, year] = dateStr.split('/').map(Number);

    let date = new Date(year, month - 1, day);

    date.setDate(date.getDate() - 7);

    const prevDay = String(date.getDate()).padStart(2, '0');
    const prevMonth = String(date.getMonth() + 1).padStart(2, '0');
    const prevYear = date.getFullYear();

    return `${prevDay}/${prevMonth}/${prevYear}`;
};

export const getCurrentDate = (): string => {
    const vietnamTime = new Date().toLocaleString('en-GB', {
        timeZone: 'Asia/Ho_Chi_Minh',
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });

    return vietnamTime.replace(',', '');
};

export const currentDateTime = () => {
    const dataTime = moment()
        .utcOffset(7)
        .utc()
        .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
    return dataTime;
};

export const convertUTCDateToLocalDate = (date: any) => {
    if (!date) {
        return '';
    }
    return new Date(
        Date.UTC(
            date.getFullYear(),
            date.getMonth(),
            date.getDate(),
            date.getHours(),
            date.getMinutes(),
            date.getSeconds()
        )
    )
        .toISOString()
        .split('T')[0];
};

export const priceToText = (price: string) => {
    return price.replace(/\D/g, '');
};
