{"compilerOptions": {"outDir": "./dist/", "sourceMap": true, "noImplicitAny": true, "module": "es6", "target": "es6", "allowJs": true, "declaration": true, "preserveSymlinks": true, "jsx": "react", "skipLibCheck": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "lib": ["es6", "dom"], "esModuleInterop": true, "strict": true, "forceConsistentCasingInFileNames": true, "noUnusedLocals": true, "noImplicitReturns": true, "baseUrl": ".", "paths": {"*": ["src/*"], "react-native-xwork": ["./node_modules/react-native-xwork"], "@common": ["src/common"], "@store": ["src/store"], "@assets": ["src/assets"], "@components": ["src/components"], "@constants": ["src/constants"], "@screens": ["src/screens"], "@elements": ["src/screens/RequestPayment/elements"], "@utils": ["./src/utils"], "@types": ["./src/types"], "@hooks": ["./src/hooks"]}}}